#!/usr/bin/env node

/**
 * Full Integration Test Script
 * Starts signaling server and runs comprehensive automation tests
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import ExtensionAutomation from './automation-script.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class FullTestRunner {
  constructor() {
    this.signalingServer = null;
    this.automation = new ExtensionAutomation();
  }

  /**
   * Start signaling server
   */
  async startSignalingServer() {
    console.log('🚀 Starting signaling server...');
    
    const serverPath = path.join(__dirname, 'signaling-server');
    
    return new Promise((resolve, reject) => {
      this.signalingServer = spawn('npm', ['start'], {
        cwd: serverPath,
        stdio: 'pipe',
        detached: false
      });

      let started = false;
      const timeout = setTimeout(() => {
        if (!started) {
          this.signalingServer.kill();
          reject(new Error('Signaling server failed to start within 15 seconds'));
        }
      }, 15000);

      this.signalingServer.stdout.on('data', (data) => {
        const output = data.toString();
        console.log(`[Server] ${output.trim()}`);
        
        if (output.includes('Server running') || output.includes('listening') || output.includes('3001')) {
          started = true;
          clearTimeout(timeout);
          console.log('✅ Signaling server started successfully');
          resolve();
        }
      });

      this.signalingServer.stderr.on('data', (data) => {
        const error = data.toString();
        console.log(`[Server Error] ${error.trim()}`);
        
        if (error.includes('EADDRINUSE')) {
          started = true;
          clearTimeout(timeout);
          console.log('✅ Signaling server already running');
          resolve();
        }
      });

      this.signalingServer.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  /**
   * Stop signaling server
   */
  stopSignalingServer() {
    if (this.signalingServer) {
      console.log('🛑 Stopping signaling server...');
      this.signalingServer.kill();
      this.signalingServer = null;
    }
  }

  /**
   * Test basic automation flow
   */
  async testBasicFlow() {
    console.log('\n🧪 Testing Basic Automation Flow');
    console.log('=' .repeat(35));

    try {
      // Step 1: Launch Chrome
      console.log('1️⃣ Launching Chrome with extension...');
      await this.automation.launchChrome();
      console.log('✅ Chrome launched');

      // Step 2: Get extension ID
      console.log('2️⃣ Getting extension ID...');
      await this.automation.getExtensionId();
      console.log(`✅ Extension ID: ${this.automation.extensionId}`);

      // Step 3: Setup test page
      console.log('3️⃣ Setting up test page...');
      await this.automation.setupTestPage();
      console.log('✅ Test page ready');

      // Step 4: Test popup access
      console.log('4️⃣ Testing popup access...');
      const popupPage = await this.automation.openExtensionPopup();
      
      // Check popup elements
      const elements = ['#startCaptureBtn', '#stopCaptureBtn', '#startWebRTCBtn', '#stopWebRTCBtn'];
      for (const selector of elements) {
        const element = await popupPage.$(selector);
        if (!element) {
          throw new Error(`Element not found: ${selector}`);
        }
      }
      
      await popupPage.close();
      console.log('✅ Popup access working');

      // Step 5: Test tab capture
      console.log('5️⃣ Testing tab capture...');
      const captureResult = await this.automation.startCapture();
      
      if (captureResult) {
        console.log('✅ Tab capture started');
        
        // Check status
        const status = await this.automation.getStatus();
        console.log(`📊 Status: ${JSON.stringify(status)}`);
        
        // Step 6: Test WebRTC (if capture successful)
        if (status.capturing) {
          console.log('6️⃣ Testing WebRTC streaming...');
          try {
            const streamResult = await this.automation.startWebRTCStream();
            if (streamResult) {
              console.log('✅ WebRTC streaming started');
            } else {
              console.log('⚠️ WebRTC streaming failed');
            }
          } catch (webrtcError) {
            console.log(`⚠️ WebRTC error: ${webrtcError.message}`);
          }
        }
        
        // Step 7: Cleanup
        console.log('7️⃣ Cleaning up...');
        await this.automation.stopStreaming();
        console.log('✅ Streaming stopped');
        
      } else {
        console.log('⚠️ Tab capture failed');
      }

      return true;

    } catch (error) {
      console.error(`❌ Test failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Test individual components
   */
  async testComponents() {
    console.log('\n🔧 Testing Individual Components');
    console.log('=' .repeat(35));

    const tests = [
      {
        name: 'Chrome Launch',
        test: async () => {
          await this.automation.launchChrome();
          return this.automation.browser !== null;
        }
      },
      {
        name: 'Extension Detection',
        test: async () => {
          await this.automation.getExtensionId();
          return this.automation.extensionId !== null;
        }
      },
      {
        name: 'Page Setup',
        test: async () => {
          await this.automation.setupTestPage();
          return this.automation.page !== null;
        }
      },
      {
        name: 'Popup Access',
        test: async () => {
          const popup = await this.automation.openExtensionPopup();
          const title = await popup.title();
          await popup.close();
          return title.includes('Tab Screen Share');
        }
      },
      {
        name: 'Status Check',
        test: async () => {
          const status = await this.automation.getStatus();
          return typeof status === 'object' && 'capturing' in status;
        }
      }
    ];

    let passed = 0;
    let failed = 0;

    for (const { name, test } of tests) {
      try {
        console.log(`Testing ${name}...`);
        const result = await test();
        if (result) {
          console.log(`✅ ${name} - PASSED`);
          passed++;
        } else {
          console.log(`❌ ${name} - FAILED`);
          failed++;
        }
      } catch (error) {
        console.log(`❌ ${name} - ERROR: ${error.message}`);
        failed++;
      }
    }

    console.log(`\n📊 Component Tests: ${passed} passed, ${failed} failed`);
    return { passed, failed };
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🎬 Full Integration Test Suite');
    console.log('=' .repeat(30));

    try {
      // Start signaling server
      await this.startSignalingServer();
      
      // Wait a bit for server to be ready
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Run component tests
      const componentResults = await this.testComponents();
      
      // Run basic flow test
      const flowResult = await this.testBasicFlow();

      // Summary
      console.log('\n🎯 Test Summary');
      console.log('=' .repeat(15));
      console.log(`Component tests: ${componentResults.passed}/${componentResults.passed + componentResults.failed}`);
      console.log(`Flow test: ${flowResult ? 'PASSED' : 'FAILED'}`);
      
      const allPassed = componentResults.failed === 0 && flowResult;
      console.log(`Overall: ${allPassed ? '✅ PASSED' : '❌ FAILED'}`);

      return allPassed;

    } catch (error) {
      console.error(`❌ Test suite failed: ${error.message}`);
      return false;
    } finally {
      // Cleanup
      await this.automation.cleanup();
      this.stopSignalingServer();
    }
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const testRunner = new FullTestRunner();
  
  process.on('SIGINT', async () => {
    console.log('\n🛑 Tests interrupted');
    await testRunner.automation.cleanup();
    testRunner.stopSignalingServer();
    process.exit(0);
  });
  
  testRunner.runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test runner failed:', error);
      process.exit(1);
    });
}

export default FullTestRunner;
