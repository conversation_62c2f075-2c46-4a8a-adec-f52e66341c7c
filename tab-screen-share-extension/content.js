// Content script for Tab Screen Share Extension
// Helps with getDisplayMedia fallback when tabCapture fails

console.log("Tab Screen Share content script loaded");

// Listen for messages from background script
chrome.runtime.onMessage.addListener((_message, _sender, _sendResponse) => {
  // Currently no content script functionality needed
  // This is a placeholder for future features like getDisplayMedia fallback
});
