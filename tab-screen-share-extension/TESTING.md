# Tab Screen Share Extension - Testing Guide

## Prerequisites

1. **Chrome Version**: Ensure you're using Chrome 116 or later for full tab capture API support
2. **Developer Mode**: Enable Developer mode in Chrome Extensions (chrome://extensions/)

## Installation Steps

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right corner
3. Click "Load unpacked" and select the `tab-screen-share-extension` folder
4. The extension should appear in your extensions list

## Testing Procedure

### 1. Basic Extension Loading
- [ ] Extension loads without errors
- [ ] Extension icon appears in the toolbar
- [ ] Clicking the icon opens the popup

### 2. Test Page Setup
1. Open the provided test page: `test-page.html` in a new tab
2. The page should display:
   - Animated background and elements
   - Audio controls for test tone generation
   - Debug information section

### 3. Tab Capture Testing

#### Test Case 1: Basic Tab Capture
1. Navigate to the test page
2. Click the extension icon to open popup
3. Verify current tab information is displayed correctly
4. Click "Start Capture" button
5. **Expected Results**:
   - Status indicator changes to "Capturing" (green dot)
   - Video preview should show the captured tab content
   - Capture information should display track counts
   - Activity log should show successful capture start

#### Test Case 2: Video Preview
1. After starting capture (Test Case 1)
2. **Expected Results**:
   - Video element in popup shows live preview of the tab
   - Preview updates in real-time as you interact with the test page
   - Animated elements on test page are visible in preview

#### Test Case 3: Stop Capture
1. While capture is active
2. Click "Stop Capture" button
3. **Expected Results**:
   - Status indicator changes to "Not Capturing" (red dot)
   - Video preview stops and clears
   - Capture information resets
   - Activity log shows successful capture stop

### 4. Error Handling Testing

#### Test Case 4: System Page Restriction
1. Navigate to a Chrome system page (e.g., `chrome://settings/`)
2. Try to start capture
3. **Expected Results**:
   - Error message: "Cannot capture system pages"
   - Capture should not start

#### Test Case 5: Extension Page Restriction
1. Navigate to `chrome://extensions/`
2. Try to start capture
3. **Expected Results**:
   - Error message about system pages
   - Capture should not start

### 5. Audio Testing
1. On the test page, click "Play Test Tone"
2. Start tab capture
3. **Expected Results**:
   - Audio tracks count should be > 0 in capture info
   - Test tone should be audible (tab audio is captured)

### 6. Multiple Tab Testing
1. Open multiple tabs with different content
2. Switch between tabs and test capture on each
3. **Expected Results**:
   - Extension should work on any regular webpage
   - Each tab can be captured independently

## Troubleshooting

### Common Issues and Solutions

1. **"Tab capture API is not available"**
   - Check Chrome version (need 116+)
   - Verify extension permissions in manifest.json

2. **"Permission denied for tab capture"**
   - Reload the extension
   - Try on a different webpage (not system pages)

3. **Video preview not working**
   - Check browser console for errors
   - Ensure tab has visual content to capture

4. **Extension popup not opening**
   - Check for JavaScript errors in extension console
   - Reload the extension

### Debugging Steps

1. **Check Extension Console**:
   - Go to `chrome://extensions/`
   - Click "Inspect views: service worker" under the extension
   - Check for errors in the console

2. **Check Popup Console**:
   - Right-click on extension popup
   - Select "Inspect" to open developer tools

3. **Check Offscreen Document**:
   - In extension console, look for offscreen document logs
   - Verify getUserMedia calls are successful

## Expected Performance

- **Capture Start Time**: < 2 seconds
- **Video Preview Latency**: < 500ms
- **Memory Usage**: Moderate (depends on tab content)
- **CPU Usage**: Moderate during active capture

## Browser Compatibility

- ✅ Chrome 116+
- ❌ Firefox (Chrome-specific APIs)
- ❌ Safari (Chrome-specific APIs)
- ❌ Edge (may work but not tested)

## Known Limitations

1. Cannot capture:
   - Chrome system pages (chrome://)
   - Extension pages
   - Some protected content (DRM videos)

2. Performance considerations:
   - High CPU usage with complex animations
   - Memory usage increases with capture duration

3. Audio considerations:
   - Tab audio is muted locally during capture
   - Audio routing requires additional setup for local playback

## Success Criteria

The extension is working correctly if:
- [x] Loads without errors
- [x] Can capture regular web pages
- [x] Shows video preview in popup
- [x] Properly handles errors and edge cases
- [x] Cleans up resources when stopped
- [x] Provides clear user feedback through UI
