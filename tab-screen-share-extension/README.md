Chrome Tab Capture and WebRTC Streaming

Building a Chrome extension to stream live video of browser tabs involves using Chrome’s media-capture APIs (and optionally the DevTools Protocol) to grab each tab’s content as a MediaStream, then feeding those streams into a WebRTC connection. Modern Chrome provides several options:

chrome.tabCapture API (manifest V3): captures the active tab’s video and audio. By design, it only works on the currently visible tab after the user invokes the extension (e.g. by clicking the action button)
developer.chrome.com
developer.chrome.com
. For example, <PERSON>’s demo extension uses chrome.tabCapture.capture({video:true, audio:true}, ...) to obtain a live stream of the active tab and then adds it to an RTCPeerConnection
developer.chrome.com
developer.chrome.com
. This proves the concept, but by itself it only handles one (active) tab. In Chrome 116+, a new method chrome.tabCapture.getMediaStreamId({targetTabId}) lets you specify any tab ID (not just active), returning a stream ID that can be passed to getUserMedia
developer.chrome.com
developer.chrome.com
. Using this, an extension can programmatically capture any open tab (after one user gesture) and turn it into a MediaStream. For example:

// In a background/service-worker context, after user action:
const tabId = /_ ID of a target tab _/;
const streamId = await chrome.tabCapture.getMediaStreamId({targetTabId: tabId});
const mediaStream = await navigator.mediaDevices.getUserMedia({
audio: { mandatory: { chromeMediaSource: 'tab', chromeMediaSourceId: streamId }},
video: { mandatory: { chromeMediaSource: 'tab', chromeMediaSourceId: streamId }}
});
// mediaStream now captures the tab’s live audio+video:contentReference[oaicite:6]{index=6}:contentReference[oaicite:7]{index=7}.

This offloads the user prompt and lets the extension capture the tab’s content in the background. (Older Chrome versions required opening a visible extension page or popup to do tabCapture; only Chrome 116+ allows using it in a service worker/offscreen context
developer.chrome.com
developer.chrome.com
.)

navigator.mediaDevices.getDisplayMedia(): triggers Chrome’s built-in screen-sharing dialog. This API can capture any selected tab, window, or screen, but requires the user to explicitly choose the source each time
developer.chrome.com
. It’s a straightforward fallback: e.g.

const stream = await navigator.mediaDevices.getDisplayMedia({video: true, audio: true});

However, this shows the “share your screen” prompt and isn’t fully automated. It’s often used with an offscreen document to keep capturing across navigations
developer.chrome.com
. In practice, extensions prefer chrome.tabCapture (which can skip the prompt after the initial click) for capturing tabs without UI.

chrome.desktopCapture (or chrome.tabs.captureVisibleTab): can capture full-screen or window. It’s mainly intended for sharing windows or the entire desktop. It doesn’t target individual tabs except by letting the user pick a specific window containing Chrome. This is less flexible for “all tabs” use case. (No need to use it if the goal is per-tab capture.)

DevTools Protocol (CDP) via chrome.debugger: For a more low-level approach, an extension can attach to any tab’s debugging session using chrome.debugger. Through CDP’s Page domain, one can invoke Page.startScreencast to receive JPEG/PNG frames of the page
medium.com
. In fact, Chrome’s remote-debugging on Android already supports screencast; on desktop one can “fake” the Page.canScreencast response to enable it
kenneth.io
. This technique effectively streams raw frames of the tab’s rendered content (with no prompt). A Node.js example shows using the DevTools Protocol to save frames from Page.screencastFrame events
medium.com
medium.com
. An extension could similarly gather these frames, pipe them into a video encoder or canvas, and feed the result to WebRTC. This approach is experimental: it requires managing the DevTools protocol messages and yields individual images rather than a continuous video track, but it would allow capturing any tab (even background) without the usual capture restrictions
kenneth.io
medium.com
.

Capturing Multiple Tabs

To capture multiple tabs in real time, the extension must create a separate MediaStream for each tab and send each through WebRTC (either over one or multiple peer connections). The high-level strategy is:

Enumerate target tabs. Use chrome.tabs.query({}) or a similar API to get the IDs of all open tabs you want to capture.

Obtain a MediaStream per tab. For each tab ID:

Call chrome.tabCapture.getMediaStreamId({targetTabId: tabId}) (Chrome 116+), or use the older workaround of opening an extension page and calling chrome.tabCapture.getMediaStreamId({targetTabId}, callback)
developer.chrome.com
.

Pass the resulting ID into getUserMedia with chromeMediaSource: "tab" to get a live MediaStream for that tab
developer.chrome.com
developer.chrome.com
.

(This must be done shortly after the user gesture that invoked the extension. In MV3, an offscreen document is often used to call getUserMedia with the ID, as shown above.)

Handle background tabs. By using targetTabId, you can capture tabs that aren’t focused. Note, however, that Chrome may throttle or freeze background tabs for performance, so captured video might pause if the tab goes idle. There is no specific API to “wake up” a frozen tab; your capture may simply be a frozen frame until the tab becomes active again. Using CDP’s screencast could circumvent this, since it directly queries the renderer.

Example code (capturing in a loop):

const pc = new RTCPeerConnection(/_ ICE servers _/);
chrome.tabs.query({}, async (tabs) => {
for (const tab of tabs) {
// Skip extension pages or uninteresting tabs as needed.
const streamId = await chrome.tabCapture.getMediaStreamId({targetTabId: tab.id});
const stream = await navigator.mediaDevices.getUserMedia({
audio: { mandatory: { chromeMediaSource: 'tab', chromeMediaSourceId: streamId }},
video: { mandatory: { chromeMediaSource: 'tab', chromeMediaSourceId: streamId }}
});
// Optionally, play audio locally to the user:
const audioCtx = new AudioContext();
audioCtx.createMediaStreamSource(stream).connect(audioCtx.destination);
// Add all tracks of this tab's stream to the WebRTC connection:
stream.getTracks().forEach(track => pc.addTrack(track, stream));
}
const offer = await pc.createOffer();
await pc.setLocalDescription(offer);
// send SDP to remote via your signaling channel...
});

Each stream in the loop is a live video+audio capture of one tab (audio may be muted locally unless re-routed as above
developer.chrome.com
). We then attach every track to the RTCPeerConnection. (In older demos, developers used the deprecated addStream(stream) instead of addTrack
developer.chrome.com
, but the idea is the same.)

WebRTC Streaming

Once you have one or more MediaStreams, standard WebRTC procedures apply. For example, after creating a RTCPeerConnection, call addTrack() for each media track (video and audio) of each tab’s stream. Then create an SDP offer/answer and exchange it with the remote peer via your signaling server. The remote client (another browser or app) will receive a track event for each stream/track you added, allowing it to render or process the videos.

Single or multiple connections: You can either send all tab streams over one PeerConnection (each video track is a separate stream on the same connection) or create separate PeerConnections per tab. Combining them into one connection saves overhead but means careful signaling so the remote side knows which track corresponds to which tab. Creating multiple connections (one per tab) is simpler conceptually but uses more resources and ports.

Remote client compatibility: Any WebRTC-capable client can receive the streams. For example, a web app can use new RTCPeerConnection() and display each received stream in a <video> element.

Audio issues: Note that when capturing a tab, Chrome by default suppresses the tab’s audio from playing locally, so the user doesn’t hear double audio. If you want the user to still hear the tab’s sound, you must re-connect the audio track to the speakers via an AudioContext (as shown above)
developer.chrome.com
. Otherwise the stream will be silent to the user and only sent to the remote.

Example from Chrome team: Sam Dutton’s extension demo shows exactly this flow. In his code, after capturing the tab stream, it does pc.addStream(localStream) and starts the standard AppRTC-style signaling
developer.chrome.com
developer.chrome.com
. That prototype proves real-time sharing of a tab via WebRTC is feasible.

Limitations and Considerations

User Gestures: Most capture APIs require a user action. The extension must be invoked by the user (e.g. clicking the icon or using a keyboard command). After that one gesture, it can start capturing. But you cannot silently capture tabs without any user consent.

Permissions: Your extension needs "tabCapture" permission in manifest.json. If using offscreen or desktop capture, you may need those permissions as well.

Performance: Capturing multiple high-resolution video streams is CPU- and bandwidth-intensive. Each tab capture encodes video (and audio) in real time. If many tabs are captured simultaneously, expect high CPU load and large outgoing bitrate. This can strain both the extension (browser) and the network/peer. Careful optimization (lower resolution, framerate, or quality) may be needed.

Background/Idle Tabs: Chrome may throttle or “freeze” inactive tabs to save resources. In such cases, the capture stream might stop updating (displaying a frozen frame). There is no official way to force a frozen tab to continue rendering. Using the DevTools Protocol approach (screencasting) could bypass this, since it can query the rendering buffer directly, but that is a more complex route.

Audio Playback: As noted, capturing a tab’s audio will mute it locally. If you want the user to hear the tab while it’s being captured, you must explicitly route the audio back to the speakers (via AudioContext)
developer.chrome.com
, or use another audio capture approach.

Browser Compatibility: The above APIs are Chrome-specific or Chrome-family (e.g. Edge). Firefox and others have different methods (getDisplayMedia for tabs/windows, or extensions APIs may differ). The question assumes Chrome-specific solutions.

MV3 Offscreen Documents: In Manifest V3, since background pages are service workers, you typically use an Offscreen Document to interact with getUserMedia or <video> elements. The official docs show exactly how to pass the stream ID to an offscreen page to call getUserMedia
developer.chrome.com
developer.chrome.com
.

Security: Users will see a tab capture indicator (a red dot) when a tab is being captured. Chrome enforces this as a privacy measure. Also, DevTools Protocol methods require the debugger permission and user approval of debugging the tab. These cannot be done silently on unknown sites without user consent.

Existing Examples and References

Several demos and projects illustrate these techniques:

Chrome Developers blog – “Screensharing with WebRTC”: An experimental extension built by Google’s Sam Dutton showed tab sharing via chrome.tabCapture and WebRTC. It captures the active tab on click and streams it to a remote peer
developer.chrome.com
developer.chrome.com
. This proves the core idea works.

Muaz Khan’s Extensions: The open-source Chrome-Extensions project and WebRTC Experiment by Muaz Khan includes examples of tab capturing and broadcasting. (Though references are not formal docs, these projects use similar APIs under the hood.)

Puppeteer/CDP Recording: Outside of extensions, the DevTools Protocol’s screencast has been used in tools. For example, Cypress and Puppeteer can record headless Chrome by calling Page.startScreencast and writing out the image frames
medium.com
medium.com
. Kenneth Auchenberg’s BrowserRemote shows that by faking Page.canScreencast, desktop Chrome can stream a tab’s viewport via DevTools
kenneth.io
. These confirm that CDP can be leveraged to get raw tab frames if needed.

In summary, the extension would use chrome.tabCapture (with getMediaStreamId) to grab each tab’s live media stream, then attach those to one or more RTCPeerConnections to stream via WebRTC. This setup respects Chrome’s permissions model and achieves real-time tab sharing, though it is bounded by user gestures and system performance.

Sources: Chrome extension docs on chrome.tabCapture
developer.chrome.com
developer.chrome.com
developer.chrome.com
developer.chrome.com
; Chrome developer blog example
developer.chrome.com
developer.chrome.com
; MDN/Chrome guides on getDisplayMedia and offscreen documents
developer.chrome.com
developer.chrome.com
; community examples (Screencast via CDP)
kenneth.io
medium.com
. Each demonstrates aspects of tab capture or streaming by WebRTC.
