# Tab Screen Share Extension

A Chrome extension that captures and shares browser tab content using Chrome's tabCapture API and WebRTC streaming capabilities.

## Features

- **Tab Capture**: Capture audio and video from any browser tab
- **Live Preview**: Real-time video preview in the extension popup
- **WebRTC Ready**: Built-in WebRTC peer connection management for streaming
- **Error Handling**: Comprehensive error handling for various edge cases
- **Modern Architecture**: Uses Chrome Manifest V3 with offscreen documents

## Installation

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right corner
3. Click "Load unpacked" and select this extension folder
4. The extension icon will appear in your toolbar

## Usage

1. **Navigate to any webpage** (not chrome:// system pages)
2. **Click the extension icon** to open the popup
3. **Click "Start Capture"** to begin capturing the current tab
4. **View the live preview** in the popup video element
5. **Click "Stop Capture"** when finished

## Requirements

- Chrome 116 or later (for full tabCapture API support)
- Regular web pages (cannot capture chrome:// system pages)

## Technical Implementation

This extension demonstrates modern Chrome extension development practices:

### Architecture Components

1. **Manifest V3**: Uses service workers and modern extension APIs
2. **Background Script** (`background.js`): Manages tab capture and WebRTC connections
3. **Offscreen Document** (`offscreen.js`): Handles getUserMedia calls in MV3
4. **Popup Interface** (`popup.js`): User interface for controlling capture
5. **WebRTC Manager** (`webrtc-manager.js`): Handles peer connections for streaming

### Key APIs Used

- `chrome.tabCapture.getMediaStreamId()`: Get stream ID for tab capture
- `navigator.mediaDevices.getUserMedia()`: Capture tab media in offscreen context
- `chrome.offscreen`: Create offscreen documents for media access
- `RTCPeerConnection`: WebRTC peer connections for streaming

### File Structure

```
tab-screen-share-extension/
├── manifest.json          # Extension configuration
├── background.js          # Service worker (main logic)
├── offscreen.html         # Offscreen document HTML
├── offscreen.js           # Media capture handler
├── popup.html             # Extension popup UI
├── popup.js               # Popup logic and controls
├── popup.css              # Popup styling
├── webrtc-manager.js      # WebRTC connection management
├── content.js             # Content script (minimal)
├── test-page.html         # Test page for capture
├── TESTING.md             # Testing instructions
└── icon.png               # Extension icon
```

## Testing

Use the included `test-page.html` for testing:
- Animated visual content
- Audio test tone generation
- Debug information display

See `TESTING.md` for detailed testing procedures.

## Limitations

- Cannot capture Chrome system pages (chrome://, chrome-extension://)
- Requires user gesture to initiate capture
- Performance depends on tab content complexity
- Audio is muted locally during capture (by design)

## WebRTC Streaming

The extension includes a WebRTC manager for streaming captured content:

```javascript
// Example usage (in background script)
await this.webrtcManager.createPeerConnection(tabId, streamId);
await this.webrtcManager.createOffer(tabId);
```

Note: Full WebRTC implementation requires a signaling server for production use.

## Development

### Building

No build process required - this is a pure JavaScript extension.

### Debugging

1. **Extension Console**: chrome://extensions/ → "Inspect views: service worker"
2. **Popup Console**: Right-click popup → "Inspect"
3. **Offscreen Console**: Check background script console for offscreen logs

### Common Issues

- **Permission errors**: Ensure all permissions are in manifest.json
- **API not available**: Check Chrome version (need 116+)
- **Capture fails**: Verify tab content is not protected/system page

## Browser Support

- ✅ Chrome 116+ (primary target)
- ❌ Firefox (uses Chrome-specific APIs)
- ❌ Safari (uses Chrome-specific APIs)

## License

This extension is provided as an example implementation. Modify as needed for your use case.

## Contributing

This is a demonstration extension. For production use, consider:
- Adding proper signaling server integration
- Implementing connection retry logic
- Adding user preferences and settings
- Optimizing performance for different content types
