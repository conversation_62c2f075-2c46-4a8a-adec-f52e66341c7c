{"manifest_version": 3, "name": "Tab Screen Share", "version": "1.0.0", "description": "Simple extension to capture and share the active tab's screen content", "permissions": ["activeTab", "tabs", "tabCapture", "desktopCapture", "scripting", "offscreen"], "background": {"service_worker": "background.js"}, "web_accessible_resources": [{"resources": ["offscreen.html"], "matches": ["<all_urls>"]}], "action": {"default_popup": "popup.html", "default_title": "Tab Screen Share", "default_icon": {"16": "icon.png", "32": "icon.png", "48": "icon.png", "128": "icon.png"}}, "icons": {"16": "icon.png", "32": "icon.png", "48": "icon.png", "128": "icon.png"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_start"}]}