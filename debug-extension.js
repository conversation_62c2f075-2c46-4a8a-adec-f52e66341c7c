#!/usr/bin/env node

/**
 * Debug script to manually test the extension step by step
 */

import ExtensionAutomation from "./automation-script.js";

class ExtensionDebugger {
  constructor() {
    this.automation = new ExtensionAutomation();
  }

  async debugCapture() {
    console.log("🔍 Debug: Starting Chrome and extension...");

    try {
      // Step 1: Launch Chrome
      await this.automation.launchChrome();
      await this.automation.getExtensionId();
      await this.automation.setupTestPage();

      console.log("✅ Chrome launched successfully");

      // Step 2: Check initial status
      console.log("\n🔍 Debug: Checking initial status...");
      const initialStatus = await this.automation.getStatus();
      console.log("Initial status:", initialStatus);

      // Step 3: Start capture with detailed monitoring
      console.log("\n🔍 Debug: Starting capture with monitoring...");
      const popupPage = await this.automation.openExtensionPopup();

      // Check if start button exists and is enabled
      const startBtn = await popupPage.$("#startCaptureBtn");
      if (!startBtn) {
        throw new Error("Start capture button not found");
      }

      const isDisabled = await popupPage.evaluate(
        (btn) => btn.disabled,
        startBtn
      );
      console.log("Start button disabled:", isDisabled);

      if (!isDisabled) {
        console.log("🔍 Debug: Clicking start capture button...");
        await popupPage.click("#startCaptureBtn");

        // Monitor status changes over time
        for (let i = 0; i < 10; i++) {
          await new Promise((resolve) => setTimeout(resolve, 1000));

          // Check button state
          const buttonDisabled = await popupPage.evaluate(
            () => document.getElementById("startCaptureBtn").disabled
          );

          // Check status text
          const statusText = await popupPage.evaluate(
            () =>
              document.querySelector(".status-text")?.textContent || "No status"
          );

          // Check for any error messages
          const errorMessages = await popupPage.evaluate(() => {
            const logs = document.querySelectorAll(".log-entry.error");
            return Array.from(logs).map((log) => log.textContent);
          });

          console.log(
            `🔍 Debug [${
              i + 1
            }s]: Button disabled: ${buttonDisabled}, Status: "${statusText}"`
          );

          if (errorMessages.length > 0) {
            console.log("❌ Errors found:", errorMessages);
          }

          if (buttonDisabled) {
            console.log("✅ Button became disabled - capture might be active");
            break;
          }
        }
      }

      await popupPage.close();

      // Step 4: Check status after capture attempt
      console.log("\n🔍 Debug: Checking status after capture attempt...");
      const finalStatus = await this.automation.getStatus();
      console.log("Final status:", finalStatus);

      // Keep browser open for manual inspection
      console.log(
        "\n🔍 Debug: Browser will stay open for manual inspection..."
      );
      console.log("Press Ctrl+C to close when done");

      // Wait for 30 seconds then cleanup automatically
      setTimeout(async () => {
        console.log("\n⏰ Auto-cleanup after 30 seconds");
        await this.cleanup();
        process.exit(0);
      }, 30000);
    } catch (error) {
      console.error("❌ Debug failed:", error.message);
    }
  }

  async cleanup() {
    await this.automation.cleanup();
  }
}

// Main execution
async function main() {
  const debug = new ExtensionDebugger();

  // Handle cleanup on exit
  process.on("SIGINT", async () => {
    console.log("\n🛑 Cleaning up...");
    await debug.cleanup();
    process.exit(0);
  });

  await debug.debugCapture();
}

main().catch(console.error);
