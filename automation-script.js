#!/usr/bin/env node

/**
 * Chrome Extension Automation Script
 * Automates tab screen sharing extension using Puppeteer and Chrome DevTools Protocol
 */

import puppeteer from "puppeteer";
import path from "path";
import fs from "fs";
import { fileURLToPath } from "url";

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ExtensionAutomation {
  constructor() {
    this.browser = null;
    this.page = null;
    this.extensionId = null;
    this.isStreaming = false;
    this.isCapturing = false;

    // Paths
    this.extensionPath = path.resolve(__dirname, "tab-screen-share-extension");
    this.signalingServerUrl = "ws://localhost:3001";
    this.webClientUrl = "http://localhost:3000";
  }

  /**
   * Launch Chrome with extension support
   */
  async launchChrome() {
    console.log("🚀 Launching Chrome with extension support...");

    // Verify extension directory exists
    if (!fs.existsSync(this.extensionPath)) {
      throw new Error(`Extension directory not found: ${this.extensionPath}`);
    }

    // Chrome launch arguments
    const args = [
      `--load-extension=${this.extensionPath}`,
      `--disable-extensions-except=${this.extensionPath}`,
      "--disable-web-security",
      "--allow-running-insecure-content",
      "--disable-features=VizDisplayCompositor",
      "--auto-accept-this-tab-capture",
      "--allow-http-screen-capture",
      "--auto-select-desktop-capture-source=Screen 1",
      "--disable-background-timer-throttling",
      "--disable-backgrounding-occluded-windows",
      "--disable-renderer-backgrounding",
    ];

    this.browser = await puppeteer.launch({
      headless: false, // Must be false for extensions
      devtools: false,
      args: args,
      defaultViewport: null,
      ignoreDefaultArgs: ["--disable-extensions"],
    });

    console.log("✅ Chrome launched successfully");
    return this.browser;
  }

  /**
   * Get extension ID from Chrome
   */
  async getExtensionId() {
    console.log("🔍 Finding extension ID...");

    // Use the hardcoded extension ID that works
    this.extensionId = "ecdamhcmpdlmieoknlngfmndkpoejgpj";
    console.log(`✅ Using extension ID: ${this.extensionId}`);
    return this.extensionId;
  }

  /**
   * Navigate to test page and setup
   */
  async setupTestPage() {
    console.log("📄 Setting up test page...");

    this.page = await this.browser.newPage();
    const cdpClient = await this.page.createCDPSession();
    // Enable CDP domains
    await cdpClient.send("Runtime.enable");
    await cdpClient.send("Page.enable");

    // Navigate to a real website that can be captured
    // Using example.com as it's a simple, reliable test page
    await this.page.goto("https://example.com");

    console.log("✅ Test page loaded");
    return this.page;
  }

  /**
   * Open extension popup programmatically
   */
  async openExtensionPopup() {
    console.log("🔧 Opening extension popup...");

    if (!this.extensionId) {
      await this.getExtensionId();
    }

    const popupUrl = `chrome-extension://${this.extensionId}/popup.html`;
    const popupPage = await this.browser.newPage();
    await popupPage.goto(popupUrl);

    console.log("✅ Extension popup opened");
    return popupPage;
  }

  /**
   * Start tab capture via extension popup
   */
  async startCapture() {
    console.log("🎥 Starting tab capture...");

    try {
      // First, ensure the content tab is active
      if (this.page) {
        await this.page.bringToFront();
        await new Promise((resolve) => setTimeout(resolve, 500)); // Wait for tab to become active
      }

      const popupPage = await this.openExtensionPopup();

      // Wait for popup to initialize
      await popupPage.waitForSelector("#startCaptureBtn", { timeout: 5000 });

      // Check if already capturing
      const startBtn = await popupPage.$("#startCaptureBtn");
      const isDisabled = await popupPage.evaluate(
        (btn) => btn.disabled,
        startBtn
      );

      if (isDisabled) {
        console.log("ℹ️ Capture already active");
        this.isCapturing = true;
        await popupPage.close();
        return true;
      }

      // Click start capture button
      await popupPage.click("#startCaptureBtn");

      // Wait for capture to start - use multiple indicators
      let captureStarted = false;
      const maxAttempts = 20; // 10 seconds total

      for (let i = 0; i < maxAttempts; i++) {
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Check if button is disabled
        const buttonDisabled = await popupPage.evaluate(
          () => document.getElementById("startCaptureBtn").disabled
        );

        // Check if status text changed
        const statusText = await popupPage.evaluate(
          () => document.querySelector(".status-text")?.textContent || ""
        );

        if (buttonDisabled || statusText.includes("Capturing")) {
          captureStarted = true;
          break;
        }

        console.log(
          `⏳ Waiting for capture to start... (${i + 1}/${maxAttempts})`
        );
      }

      if (!captureStarted) {
        // Check for error messages in the popup
        const errorMessages = await popupPage.evaluate(() => {
          const logs = document.querySelectorAll(".log-entry.error");
          return Array.from(logs).map((log) => log.textContent);
        });

        if (errorMessages.length > 0) {
          throw new Error(
            `Capture failed: ${errorMessages[errorMessages.length - 1]}`
          );
        } else {
          throw new Error("Capture did not start within timeout period");
        }
      }

      this.isCapturing = true;
      console.log("✅ Tab capture started successfully");

      await popupPage.close();
      return true;
    } catch (error) {
      console.error("❌ Failed to start capture:", error.message);
      return false;
    }
  }

  /**
   * Start WebRTC streaming
   */
  async startWebRTCStream() {
    console.log("🌐 Starting WebRTC stream...");

    if (!this.isCapturing) {
      console.log("⚠️ Starting capture first...");
      const captureSuccess = await this.startCapture();
      if (!captureSuccess) {
        throw new Error("Failed to start capture before WebRTC");
      }
    }

    try {
      const popupPage = await this.openExtensionPopup();

      // Wait for WebRTC button to be available
      await popupPage.waitForSelector("#startWebRTCBtn", { timeout: 5000 });

      // Check if WebRTC button is enabled
      const webrtcBtn = await popupPage.$("#startWebRTCBtn");
      const isDisabled = await popupPage.evaluate(
        (btn) => btn.disabled,
        webrtcBtn
      );

      if (isDisabled) {
        throw new Error(
          "WebRTC button is disabled - capture may not be active"
        );
      }

      // Click start WebRTC button
      await popupPage.click("#startWebRTCBtn");

      // Wait for streaming to start
      await popupPage.waitForFunction(
        () => document.getElementById("startWebRTCBtn").disabled,
        { timeout: 15000 }
      );

      this.isStreaming = true;
      console.log("✅ WebRTC streaming started successfully");

      await popupPage.close();
      return true;
    } catch (error) {
      console.error("❌ Failed to start WebRTC stream:", error.message);
      return false;
    }
  }

  /**
   * Stop streaming and capture
   */
  async stopStreaming() {
    console.log("🛑 Stopping streaming...");

    try {
      const popupPage = await this.openExtensionPopup();

      // Stop WebRTC if streaming
      if (this.isStreaming) {
        const stopWebRTCBtn = await popupPage.$("#stopWebRTCBtn");
        if (stopWebRTCBtn) {
          const isEnabled = await popupPage.evaluate(
            (btn) => !btn.disabled,
            stopWebRTCBtn
          );
          if (isEnabled) {
            await popupPage.click("#stopWebRTCBtn");
            console.log("✅ WebRTC streaming stopped");
          }
        }
        this.isStreaming = false;
      }

      // Stop capture if active
      if (this.isCapturing) {
        const stopCaptureBtn = await popupPage.$("#stopCaptureBtn");
        if (stopCaptureBtn) {
          const isEnabled = await popupPage.evaluate(
            (btn) => !btn.disabled,
            stopCaptureBtn
          );
          if (isEnabled) {
            await popupPage.click("#stopCaptureBtn");
            console.log("✅ Tab capture stopped");
          }
        }
        this.isCapturing = false;
      }

      await popupPage.close();
      return true;
    } catch (error) {
      console.error("❌ Failed to stop streaming:", error.message);
      return false;
    }
  }

  /**
   * Get current streaming status
   */
  async getStatus() {
    try {
      const popupPage = await this.openExtensionPopup();

      // Check button states to determine status
      const startCaptureDisabled = await popupPage.evaluate(
        () => document.getElementById("startCaptureBtn").disabled
      );
      const startWebRTCDisabled = await popupPage.evaluate(
        () => document.getElementById("startWebRTCBtn").disabled
      );
      const stopWebRTCDisabled = await popupPage.evaluate(
        () => document.getElementById("stopWebRTCBtn").disabled
      );

      this.isCapturing = startCaptureDisabled;
      this.isStreaming = !stopWebRTCDisabled;

      await popupPage.close();

      const status = {
        capturing: this.isCapturing,
        streaming: this.isStreaming,
        ready: this.isCapturing && !this.isStreaming,
      };

      console.log("📊 Current status:", status);
      return status;
    } catch (error) {
      console.error("❌ Failed to get status:", error.message);
      return { capturing: false, streaming: false, ready: false };
    }
  }

  /**
   * Cleanup and close browser
   */
  async cleanup() {
    console.log("🧹 Cleaning up...");

    if (this.isStreaming || this.isCapturing) {
      await this.stopStreaming();
    }

    if (this.browser) {
      await this.browser.close();
      console.log("✅ Browser closed");
    }
  }
}

export default ExtensionAutomation;
