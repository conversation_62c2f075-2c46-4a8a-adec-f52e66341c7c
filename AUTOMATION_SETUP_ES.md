# Chrome Extension Automation - ES Module Setup

## 🔧 Quick Setup for ES Modules

Your project is configured as an ES module, so I've converted all automation scripts to use ES module syntax.

### 1. Install Dependencies

```bash
# Copy the automation package.json
cp automation-package.json package.json

# Install Puppeteer
npm install puppeteer
```

### 2. Run Setup Script

```bash
# Run the automated setup
node setup-automation.js
```

### 3. Test the Automation

```bash
# Run interactive demo
node demo-automation.js interactive

# Or run automated demo
node demo-automation.js auto

# Or use CLI interface
node automation-cli.js interactive
```

## 🎯 Key Changes Made

### ES Module Conversion

All scripts have been converted from CommonJS to ES modules:

**Before (CommonJS):**
```javascript
const ExtensionAutomation = require('./automation-script');
module.exports = AutomationCLI;
```

**After (ES Modules):**
```javascript
import ExtensionAutomation from './automation-script.js';
export default AutomationCLI;
```

### File Extensions

- All imports now include `.js` extension
- Added `"type": "module"` to package.json
- Used `import.meta.url` instead of `require.main === module`

### __dirname Equivalent

```javascript
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
```

## 🚀 Usage Examples

### CLI Commands

```bash
# Initialize automation system
node automation-cli.js init

# Start streaming
node automation-cli.js start

# Check status
node automation-cli.js status

# Interactive mode
node automation-cli.js interactive
```

### Programmatic Usage

```javascript
import ExtensionAutomation from './automation-script.js';

const automation = new ExtensionAutomation();

try {
  await automation.launchChrome();
  await automation.getExtensionId();
  await automation.setupTestPage();
  
  // Start streaming
  await automation.startCapture();
  await automation.startWebRTCStream();
  
  console.log('Streaming started!');
  
} finally {
  await automation.cleanup();
}
```

### Demo Scripts

```bash
# Automated demo
node demo-automation.js auto

# Interactive demo with prompts
node demo-automation.js interactive

# Function tests
node demo-automation.js test
```

## 🔍 Troubleshooting

### Common ES Module Issues

**Import Error:**
```
SyntaxError: Cannot use import statement outside a module
```
**Solution:** Ensure `"type": "module"` is in package.json

**File Extension Error:**
```
Error [ERR_MODULE_NOT_FOUND]: Cannot resolve module
```
**Solution:** Add `.js` extension to all imports

**__dirname Error:**
```
ReferenceError: __dirname is not defined
```
**Solution:** Use the fileURLToPath pattern shown above

### Verification Steps

1. **Check package.json:**
   ```json
   {
     "type": "module",
     "dependencies": {
       "puppeteer": "^21.5.0"
     }
   }
   ```

2. **Test import:**
   ```bash
   node -e "import('./automation-script.js').then(m => console.log('✅ Import works'))"
   ```

3. **Run setup:**
   ```bash
   node setup-automation.js
   ```

## 📋 File Structure

```
session-inject/
├── package.json                    # ES module config
├── automation-script.js            # Core automation (ES module)
├── automation-cli.js               # CLI interface (ES module)
├── demo-automation.js              # Demo scripts (ES module)
├── setup-automation.js             # Setup script (ES module)
├── tab-screen-share-extension/     # Chrome extension
├── signaling-server/               # WebRTC signaling
└── AUTOMATION_GUIDE.md             # Detailed documentation
```

## 🎉 Ready to Use!

The automation system is now fully compatible with your ES module project. You can:

1. **Start with setup:** `node setup-automation.js`
2. **Try the demo:** `node demo-automation.js interactive`
3. **Use CLI:** `node automation-cli.js interactive`
4. **Integrate programmatically** using ES module imports

All functionality remains the same - only the module syntax has been updated for compatibility!
