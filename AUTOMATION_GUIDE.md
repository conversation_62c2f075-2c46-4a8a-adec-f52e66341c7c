# Chrome Extension Automation Guide

## 🤖 Overview

This automation system provides programmatic control over the Chrome tab screen sharing extension using Puppeteer and Chrome DevTools Protocol (CDP). It enables automated testing, continuous integration, and remote control of the streaming functionality.

## 📋 Features

- **🚀 Automated Chrome Launch**: Launches Chrome with extension pre-loaded
- **🎥 Tab Capture Control**: Programmatically start/stop tab capture
- **🌐 WebRTC Management**: Automate WebRTC streaming setup and teardown
- **📊 Status Monitoring**: Real-time status checking and reporting
- **🔧 CLI Interface**: Command-line tools for easy control
- **🎮 Interactive Mode**: Step-by-step manual control
- **🧪 Testing Suite**: Comprehensive function testing

## 🛠️ Setup Instructions

### 1. Install Dependencies

```bash
# Install Puppeteer for automation
npm install puppeteer

# Or copy the provided package.json
cp automation-package.json package.json
npm install
```

### 2. Verify Extension Structure

Ensure your directory structure looks like this:
```
session-inject/
├── tab-screen-share-extension/     # Chrome extension
├── signaling-server/               # WebRTC signaling server
├── automation-script.js            # Core automation class
├── automation-cli.js               # CLI interface
├── demo-automation.js              # Demo scripts
└── package.json                    # Dependencies
```

### 3. Start Signaling Server

The automation will auto-start the signaling server, but you can start it manually:

```bash
cd signaling-server
npm start
```

## 🎯 Usage Examples

### Basic CLI Commands

```bash
# Initialize the automation system
node automation-cli.js init

# Start complete streaming pipeline
node automation-cli.js start

# Check current status
node automation-cli.js status

# Stop streaming
node automation-cli.js stop

# Interactive mode
node automation-cli.js interactive
```

### Programmatic Usage

```javascript
const ExtensionAutomation = require('./automation-script');

async function example() {
  const automation = new ExtensionAutomation();
  
  try {
    // Initialize
    await automation.launchChrome();
    await automation.getExtensionId();
    await automation.setupTestPage();
    
    // Start streaming
    await automation.startCapture();
    await automation.startWebRTCStream();
    
    // Check status
    const status = await automation.getStatus();
    console.log('Streaming status:', status);
    
    // Stop when done
    await automation.stopStreaming();
    
  } finally {
    await automation.cleanup();
  }
}
```

### Demo Scripts

```bash
# Run automated demo
node demo-automation.js auto

# Run interactive demo with prompts
node demo-automation.js interactive

# Run function tests
node demo-automation.js test
```

## 🔧 API Reference

### ExtensionAutomation Class

#### Core Methods

- **`launchChrome()`** - Launch Chrome with extension loaded
- **`getExtensionId()`** - Find and return extension ID
- **`setupTestPage()`** - Navigate to test page
- **`startCapture()`** - Start tab capture
- **`startWebRTCStream()`** - Start WebRTC streaming
- **`stopStreaming()`** - Stop all streaming and capture
- **`getStatus()`** - Get current status
- **`cleanup()`** - Clean up resources

#### Status Object

```javascript
{
  capturing: boolean,    // Tab capture active
  streaming: boolean,    // WebRTC streaming active
  ready: boolean        // Ready to start streaming
}
```

### CLI Commands

| Command | Description |
|---------|-------------|
| `init` | Initialize automation system |
| `start` | Start complete streaming pipeline |
| `stop` | Stop streaming and capture |
| `status` | Get current status |
| `interactive` | Enter interactive mode |

## 🎮 Interactive Mode

Interactive mode provides step-by-step control:

```bash
node automation-cli.js interactive

# Available commands in interactive mode:
automation> start    # Start streaming
automation> stop     # Stop streaming  
automation> status   # Check status
automation> quit     # Exit
```

## 🧪 Testing and Validation

### Automated Testing

```bash
# Run comprehensive function tests
node demo-automation.js test
```

### Manual Validation

1. **Extension Loading**: Verify extension appears in Chrome toolbar
2. **Tab Capture**: Check for red recording indicator in tab
3. **WebRTC Streaming**: Confirm stream appears at http://localhost:3000
4. **Status Accuracy**: Verify status matches actual state

### Expected Behavior

| Action | Expected Result |
|--------|----------------|
| Launch Chrome | Extension icon visible in toolbar |
| Start Capture | Red recording dot in tab, "Capturing" status |
| Start WebRTC | Stream visible in web client |
| Stop Streaming | All indicators reset to inactive |

## 🔍 Troubleshooting

### Common Issues

**Extension Not Loading**
```bash
# Check extension path
ls -la tab-screen-share-extension/manifest.json

# Verify Chrome args
# Extension path must be absolute
```

**Capture Fails**
```bash
# Check permissions in manifest.json
# Ensure test page is not a chrome:// URL
# Verify Chrome flags for media access
```

**WebRTC Connection Fails**
```bash
# Check signaling server
curl http://localhost:3001/status

# Verify WebSocket connection
# Check browser console for errors
```

**Popup Not Found**
```bash
# Extension ID detection failed
# Check service worker in chrome://extensions/
# Verify extension is enabled
```

### Debug Mode

Enable verbose logging:

```javascript
// Add to automation script
console.log('Debug: Extension ID:', this.extensionId);
console.log('Debug: Page URL:', await this.page.url());
```

### Chrome DevTools

Access extension debugging:
1. Go to `chrome://extensions/`
2. Click "Inspect views: service worker"
3. Check console for errors

## 🚀 Advanced Usage

### Custom Chrome Flags

```javascript
const automation = new ExtensionAutomation();

// Modify Chrome args before launch
automation.chromeArgs = [
  '--use-fake-ui-for-media-stream',
  '--use-fake-device-for-media-stream',
  '--allow-http-screen-capture'
];
```

### Integration with CI/CD

```yaml
# GitHub Actions example
- name: Run Extension Tests
  run: |
    npm install
    node demo-automation.js test
```

### Remote Control

```javascript
// HTTP API wrapper example
app.post('/start-streaming', async (req, res) => {
  try {
    await automation.startStreaming();
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

## 📊 Performance Considerations

- **Memory Usage**: Chrome with extensions uses ~200-500MB
- **CPU Usage**: Screen capture adds ~10-20% CPU load
- **Network**: WebRTC streaming uses ~1-5 Mbps
- **Startup Time**: Full initialization takes ~5-10 seconds

## 🔒 Security Notes

- Extension runs with elevated permissions
- Signaling server should be secured in production
- Screen capture exposes sensitive information
- Use HTTPS in production environments

## 📝 Logging and Monitoring

The automation system provides comprehensive logging:

- **✅ Success indicators** for completed operations
- **❌ Error messages** with detailed failure reasons
- **📊 Status reports** showing current state
- **⏱️ Timing information** for performance monitoring

All logs include clear emoji indicators for easy visual parsing.

This automation system provides a robust foundation for testing, monitoring, and controlling the Chrome extension programmatically!
