#!/usr/bin/env node

/**
 * Debug Script for Chrome Extension Automation
 * Provides detailed debugging and troubleshooting information
 */

import ExtensionAutomation from "./automation-script.js";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class AutomationDebugger {
  constructor() {
    this.automation = new ExtensionAutomation();
  }

  /**
   * Debug Chrome launch and extension loading
   */
  async debugChromeLaunch() {
    console.log("🔍 Debugging Chrome Launch");
    console.log("=".repeat(30));

    try {
      // Check extension path
      const extensionPath = this.automation.extensionPath;
      console.log(`Extension path: ${extensionPath}`);
      console.log(`Extension exists: ${fs.existsSync(extensionPath)}`);

      // Check manifest
      const manifestPath = path.join(extensionPath, "manifest.json");
      if (fs.existsSync(manifestPath)) {
        const manifest = JSON.parse(fs.readFileSync(manifestPath, "utf8"));
        console.log(`Manifest version: ${manifest.manifest_version}`);
        console.log(`Extension name: ${manifest.name}`);
        console.log(`Extension version: ${manifest.version}`);

        // Check permissions
        console.log("Permissions:", manifest.permissions);
      }

      // Launch Chrome with debug info
      console.log("\n🚀 Launching Chrome...");
      await this.automation.launchChrome();

      // Get browser info
      const version = await this.automation.browser.version();
      console.log(`Chrome version: ${version}`);

      // Check targets
      const targets = await this.automation.browser.targets();
      console.log(`\nBrowser targets (${targets.length}):`);
      targets.forEach((target, index) => {
        console.log(
          `  ${index + 1}. Type: ${target.type()}, URL: ${target.url()}`
        );
      });

      return true;
    } catch (error) {
      console.error("❌ Chrome launch failed:", error);
      return false;
    }
  }

  /**
   * Debug extension detection
   */
  async debugExtensionDetection() {
    console.log("\n🔍 Debugging Extension Detection");
    console.log("=".repeat(35));

    try {
      // Manual extension ID detection
      const targets = await this.automation.browser.targets();
      const serviceWorkers = targets.filter(
        (target) => target.type() === "service_worker"
      );

      console.log(`Service workers found: ${serviceWorkers.length}`);
      serviceWorkers.forEach((sw, index) => {
        const url = sw.url();
        console.log(`  ${index + 1}. ${url}`);

        if (url.includes("chrome-extension://")) {
          const extensionId = url.split("/")[2];
          console.log(`    Extension ID: ${extensionId}`);
        }
      });

      // Try to get extension ID
      await this.automation.getExtensionId();
      console.log(`Detected extension ID: ${this.automation.extensionId}`);

      return true;
    } catch (error) {
      console.error("❌ Extension detection failed:", error);
      return false;
    }
  }

  /**
   * Debug popup access
   */
  async debugPopupAccess() {
    console.log("\n🔍 Debugging Popup Access");
    console.log("=".repeat(30));

    try {
      if (!this.automation.extensionId) {
        await this.automation.getExtensionId();
      }

      const popupUrl = `chrome-extension://${this.automation.extensionId}/popup.html`;
      console.log(`Popup URL: ${popupUrl}`);

      // Try to open popup
      const popupPage = await this.automation.browser.newPage();

      console.log("Navigating to popup...");
      await popupPage.goto(popupUrl);

      // Check if popup loaded
      const title = await popupPage.title();
      console.log(`Popup title: ${title}`);

      // Check popup content
      const bodyText = await popupPage.evaluate(() => document.body.innerText);
      console.log(`Popup content length: ${bodyText.length} characters`);

      // Check for specific elements
      const elements = [
        "#startCaptureBtn",
        "#stopCaptureBtn",
        "#startWebRTCBtn",
        "#stopWebRTCBtn",
        "#captureStatus",
      ];

      console.log("\nPopup elements:");
      for (const selector of elements) {
        const element = await popupPage.$(selector);
        const exists = element !== null;
        console.log(`  ${selector}: ${exists ? "✅" : "❌"}`);

        if (exists) {
          const isVisible = await popupPage.evaluate((sel) => {
            const el = document.querySelector(sel);
            return el && el.offsetParent !== null;
          }, selector);
          const isDisabled = await popupPage.evaluate((sel) => {
            const el = document.querySelector(sel);
            return el && el.disabled;
          }, selector);

          console.log(`    Visible: ${isVisible}, Disabled: ${isDisabled}`);
        }
      }

      // Check console errors
      const errors = [];
      popupPage.on("console", (msg) => {
        if (msg.type() === "error") {
          errors.push(msg.text());
        }
      });

      await new Promise((resolve) => setTimeout(resolve, 2000));

      if (errors.length > 0) {
        console.log("\nPopup console errors:");
        errors.forEach((error) => console.log(`  ❌ ${error}`));
      } else {
        console.log("\n✅ No popup console errors");
      }

      await popupPage.close();
      return true;
    } catch (error) {
      console.error("❌ Popup access failed:", error);
      return false;
    }
  }

  /**
   * Debug tab capture
   */
  async debugTabCapture() {
    console.log("\n🔍 Debugging Tab Capture");
    console.log("=".repeat(28));

    try {
      // Setup test page first
      await this.automation.setupTestPage();

      const testUrl = this.automation.page.url();
      console.log(`Test page URL: ${testUrl}`);

      // Check if page is capturable
      const isSystemPage =
        testUrl.startsWith("chrome://") ||
        testUrl.startsWith("chrome-extension://") ||
        testUrl.startsWith("edge://") ||
        testUrl.startsWith("about:");

      console.log(`Is system page: ${isSystemPage}`);

      if (isSystemPage) {
        console.log("⚠️ Warning: System pages cannot be captured");
      }

      // Try to start capture
      console.log("\nAttempting to start capture...");

      const popupPage = await this.automation.openExtensionPopup();

      // Check initial button state
      const startBtn = await popupPage.$("#startCaptureBtn");
      if (startBtn) {
        const isDisabled = await popupPage.evaluate(
          (btn) => btn.disabled,
          startBtn
        );
        console.log(`Start capture button disabled: ${isDisabled}`);
      }

      // Monitor console messages
      const messages = [];
      popupPage.on("console", (msg) => {
        messages.push(`${msg.type()}: ${msg.text()}`);
      });

      // Try clicking start capture
      try {
        await popupPage.click("#startCaptureBtn");
        console.log("✅ Clicked start capture button");

        // Wait and check for changes
        await new Promise((resolve) => setTimeout(resolve, 3000));

        const isNowDisabled = await popupPage.evaluate(
          () => document.getElementById("startCaptureBtn").disabled
        );
        console.log(`Button disabled after click: ${isNowDisabled}`);
      } catch (clickError) {
        console.error("❌ Failed to click start capture:", clickError.message);
      }

      // Show console messages
      if (messages.length > 0) {
        console.log("\nPopup console messages:");
        messages.forEach((msg) => console.log(`  ${msg}`));
      }

      await popupPage.close();
      return true;
    } catch (error) {
      console.error("❌ Tab capture debug failed:", error);
      return false;
    }
  }

  /**
   * Debug WebRTC functionality
   */
  async debugWebRTC() {
    console.log("\n🔍 Debugging WebRTC");
    console.log("=".repeat(22));

    try {
      // Check if capture is active
      const status = await this.automation.getStatus();
      console.log(`Current status: ${JSON.stringify(status)}`);

      if (!status.capturing) {
        console.log("⚠️ Capture not active, starting capture first...");
        await this.automation.startCapture();
      }

      // Check signaling server
      console.log("\nChecking signaling server...");
      try {
        const http = await import("http");
        const serverCheck = await new Promise((resolve) => {
          const req = http.get("http://localhost:3001/status", (res) => {
            resolve(res.statusCode === 200);
          });
          req.on("error", () => resolve(false));
          req.setTimeout(2000, () => {
            req.destroy();
            resolve(false);
          });
        });

        console.log(`Signaling server running: ${serverCheck}`);
      } catch (serverError) {
        console.log("❌ Could not check signaling server");
      }

      // Try WebRTC streaming
      console.log("\nAttempting WebRTC streaming...");

      const popupPage = await this.automation.openExtensionPopup();

      // Check WebRTC button state
      const webrtcBtn = await popupPage.$("#startWebRTCBtn");
      if (webrtcBtn) {
        const isDisabled = await popupPage.evaluate(
          (btn) => btn.disabled,
          webrtcBtn
        );
        console.log(`WebRTC button disabled: ${isDisabled}`);

        if (!isDisabled) {
          try {
            await popupPage.click("#startWebRTCBtn");
            console.log("✅ Clicked WebRTC button");

            await new Promise((resolve) => setTimeout(resolve, 3000));

            const isNowDisabled = await popupPage.evaluate(
              () => document.getElementById("startWebRTCBtn").disabled
            );
            console.log(`WebRTC button disabled after click: ${isNowDisabled}`);
          } catch (clickError) {
            console.error(
              "❌ Failed to click WebRTC button:",
              clickError.message
            );
          }
        }
      }

      await popupPage.close();
      return true;
    } catch (error) {
      console.error("❌ WebRTC debug failed:", error);
      return false;
    }
  }

  /**
   * Run comprehensive debug
   */
  async runFullDebug() {
    console.log("🔍 Chrome Extension Automation Debug");
    console.log("=".repeat(40));

    try {
      await this.debugChromeLaunch();
      await this.debugExtensionDetection();
      await this.debugPopupAccess();
      await this.debugTabCapture();
      await this.debugWebRTC();

      console.log("\n✅ Debug completed");
    } catch (error) {
      console.error("❌ Debug failed:", error);
    } finally {
      await this.automation.cleanup();
    }
  }
}

// Run debug if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const debug = new AutomationDebugger();

  process.on("SIGINT", async () => {
    console.log("\n🛑 Debug interrupted");
    await debug.automation.cleanup();
    process.exit(0);
  });

  debug.runFullDebug();
}

export default AutomationDebugger;
