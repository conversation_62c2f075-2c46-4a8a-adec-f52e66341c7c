<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Screen Share - Web Client</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .header {
            background: #2d2d2d;
            padding: 15px 20px;
            border-bottom: 1px solid #444;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .title {
            font-size: 20px;
            font-weight: 600;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #ff4757;
        }

        .status-dot.connected {
            background: #2ed573;
        }

        .status-dot.streaming {
            background: #ffa502;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .controls {
            background: #2d2d2d;
            padding: 10px 20px;
            border-bottom: 1px solid #444;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .room-input {
            padding: 8px 12px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #3d3d3d;
            color: white;
            font-size: 14px;
        }

        .main-content {
            flex: 1;
            display: flex;
            position: relative;
            background: #000;
        }

        .video-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        #remoteVideo {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
        }

        .no-stream {
            text-align: center;
            color: #888;
        }

        .no-stream h2 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .sidebar {
            width: 300px;
            background: #2d2d2d;
            border-left: 1px solid #444;
            display: flex;
            flex-direction: column;
        }

        .sidebar-section {
            padding: 15px;
            border-bottom: 1px solid #444;
        }

        .sidebar-section h3 {
            font-size: 16px;
            margin-bottom: 10px;
            color: #fff;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .info-label {
            color: #aaa;
        }

        .info-value {
            color: #fff;
            font-weight: 500;
        }

        .log-container {
            flex: 1;
            background: #1a1a1a;
            padding: 15px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.info {
            color: #17a2b8;
        }

        .log-entry.success {
            color: #28a745;
        }

        .log-entry.warning {
            color: #ffc107;
        }

        .log-entry.error {
            color: #dc3545;
        }

        .fullscreen-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .fullscreen-btn:hover {
            background: rgba(0, 0, 0, 0.9);
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="title">Tab Screen Share - Web Client</div>
            <div class="status">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">Disconnected</span>
            </div>
        </header>

        <div class="controls">
            <input type="text" id="roomInput" class="room-input" placeholder="Room ID (default: default-room)" value="default-room">
            <button id="connectBtn" class="btn btn-primary">Connect</button>
            <button id="disconnectBtn" class="btn btn-secondary" disabled>Disconnect</button>
            <span style="margin-left: auto; font-size: 14px; color: #aaa;">
                WebSocket: <span id="wsStatus">Disconnected</span>
            </span>
        </div>

        <div class="main-content">
            <div class="video-container">
                <video id="remoteVideo" autoplay playsinline controls style="display: none;"></video>
                <div class="no-stream" id="noStreamMessage">
                    <h2>No Stream Available</h2>
                    <p>Connect to a room and wait for a Chrome extension to start streaming</p>
                </div>
                <button class="fullscreen-btn" id="fullscreenBtn" style="display: none;">Fullscreen</button>
            </div>

            <div class="sidebar">
                <div class="sidebar-section">
                    <h3>Connection Info</h3>
                    <div class="info-item">
                        <span class="info-label">Client ID:</span>
                        <span class="info-value" id="clientId">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Room:</span>
                        <span class="info-value" id="currentRoom">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Peer State:</span>
                        <span class="info-value" id="peerState">-</span>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>Stream Info</h3>
                    <div class="info-item">
                        <span class="info-label">Video Tracks:</span>
                        <span class="info-value" id="videoTracks">0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Audio Tracks:</span>
                        <span class="info-value" id="audioTracks">0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Resolution:</span>
                        <span class="info-value" id="resolution">-</span>
                    </div>
                </div>

                <div class="log-container" id="logContainer">
                    <div class="log-entry info">Web client initialized</div>
                </div>
            </div>
        </div>
    </div>

    <script src="client.js"></script>
</body>
</html>
