class WebRTCClient {
  constructor() {
    this.ws = null;
    this.pc = null;
    this.clientId = null;
    this.currentRoom = null;
    this.isConnected = false;
    
    this.remoteVideo = document.getElementById('remoteVideo');
    this.noStreamMessage = document.getElementById('noStreamMessage');
    this.fullscreenBtn = document.getElementById('fullscreenBtn');
    
    this.setupEventListeners();
    this.setupWebRTC();
    
    this.log('Web client initialized', 'info');
  }

  setupEventListeners() {
    document.getElementById('connectBtn').addEventListener('click', () => this.connect());
    document.getElementById('disconnectBtn').addEventListener('click', () => this.disconnect());
    document.getElementById('fullscreenBtn').addEventListener('click', () => this.toggleFullscreen());
    
    // Auto-connect on Enter in room input
    document.getElementById('roomInput').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.connect();
      }
    });
  }

  setupWebRTC() {
    const configuration = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ]
    };

    this.pc = new RTCPeerConnection(configuration);

    this.pc.onicecandidate = (event) => {
      if (event.candidate && this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.sendMessage({
          type: 'ice-candidate',
          candidate: event.candidate,
          targetClientId: this.extensionClientId
        });
      }
    };

    this.pc.ontrack = (event) => {
      this.log('Received remote stream', 'success');
      this.remoteVideo.srcObject = event.streams[0];
      this.showVideo();
      this.updateStreamInfo(event.streams[0]);
    };

    this.pc.onconnectionstatechange = () => {
      this.log(`Connection state: ${this.pc.connectionState}`, 'info');
      this.updateUI();
      
      if (this.pc.connectionState === 'connected') {
        this.updateStatus('streaming', 'Streaming');
      } else if (this.pc.connectionState === 'failed') {
        this.log('WebRTC connection failed', 'error');
        this.updateStatus('connected', 'Connected (No Stream)');
      }
    };
  }

  connect() {
    const roomId = document.getElementById('roomInput').value.trim() || 'default-room';
    
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.disconnect();
    }

    this.log(`Connecting to room: ${roomId}`, 'info');
    
    // Connect to WebSocket server
    const wsUrl = `ws://${window.location.hostname}:3001`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      this.log('WebSocket connected', 'success');
      this.updateWSStatus('Connected');
      
      // Register as client
      this.sendMessage({
        type: 'register',
        clientType: 'client'
      });
    };

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        this.log(`Invalid message: ${error.message}`, 'error');
      }
    };

    this.ws.onclose = () => {
      this.log('WebSocket disconnected', 'warning');
      this.updateWSStatus('Disconnected');
      this.updateStatus('disconnected', 'Disconnected');
      this.isConnected = false;
      this.updateUI();
    };

    this.ws.onerror = (error) => {
      this.log(`WebSocket error: ${error}`, 'error');
    };
  }

  disconnect() {
    if (this.pc) {
      this.pc.close();
      this.setupWebRTC(); // Create new peer connection
    }
    
    if (this.ws) {
      this.ws.close();
    }
    
    this.hideVideo();
    this.isConnected = false;
    this.currentRoom = null;
    this.updateUI();
    this.log('Disconnected', 'info');
  }

  sendMessage(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  handleMessage(message) {
    switch (message.type) {
      case 'welcome':
        this.clientId = message.clientId;
        this.updateClientInfo();
        this.log(`Assigned client ID: ${this.clientId}`, 'info');
        break;

      case 'registered':
        this.log('Registered as client', 'success');
        // Join the specified room
        const roomId = document.getElementById('roomInput').value.trim() || 'default-room';
        this.sendMessage({
          type: 'join-room',
          roomId: roomId
        });
        break;

      case 'room-joined':
        this.currentRoom = message.roomId;
        this.isConnected = true;
        this.updateStatus('connected', 'Connected');
        this.updateClientInfo();
        this.updateUI();
        this.log(`Joined room: ${message.roomId}`, 'success');
        break;

      case 'client-joined':
        if (message.clientType === 'extension') {
          this.extensionClientId = message.clientId;
          this.log(`Extension joined room: ${message.clientId}`, 'info');
        }
        break;

      case 'stream-available':
        this.log('Stream available from extension', 'success');
        this.extensionClientId = message.fromClientId;
        // Extension will send offer, we just wait
        break;

      case 'offer':
        this.handleOffer(message);
        break;

      case 'ice-candidate':
        this.handleIceCandidate(message);
        break;

      case 'stream-stopped':
        this.log('Stream stopped by extension', 'warning');
        this.hideVideo();
        break;

      default:
        this.log(`Unknown message type: ${message.type}`, 'warning');
    }
  }

  async handleOffer(message) {
    try {
      this.log('Received offer from extension', 'info');
      this.extensionClientId = message.fromClientId;
      
      await this.pc.setRemoteDescription(message.offer);
      const answer = await this.pc.createAnswer();
      await this.pc.setLocalDescription(answer);

      this.sendMessage({
        type: 'answer',
        answer: answer,
        targetClientId: this.extensionClientId
      });

      this.log('Sent answer to extension', 'success');
    } catch (error) {
      this.log(`Error handling offer: ${error.message}`, 'error');
    }
  }

  async handleIceCandidate(message) {
    try {
      await this.pc.addIceCandidate(message.candidate);
      this.log('Added ICE candidate', 'info');
    } catch (error) {
      this.log(`Error adding ICE candidate: ${error.message}`, 'error');
    }
  }

  showVideo() {
    this.remoteVideo.style.display = 'block';
    this.noStreamMessage.style.display = 'none';
    this.fullscreenBtn.style.display = 'block';
  }

  hideVideo() {
    this.remoteVideo.style.display = 'none';
    this.remoteVideo.srcObject = null;
    this.noStreamMessage.style.display = 'block';
    this.fullscreenBtn.style.display = 'none';
    this.updateStreamInfo(null);
  }

  updateStreamInfo(stream) {
    if (stream) {
      const videoTracks = stream.getVideoTracks().length;
      const audioTracks = stream.getAudioTracks().length;
      
      document.getElementById('videoTracks').textContent = videoTracks;
      document.getElementById('audioTracks').textContent = audioTracks;
      
      // Get video resolution if available
      const videoTrack = stream.getVideoTracks()[0];
      if (videoTrack) {
        const settings = videoTrack.getSettings();
        if (settings.width && settings.height) {
          document.getElementById('resolution').textContent = `${settings.width}x${settings.height}`;
        }
      }
    } else {
      document.getElementById('videoTracks').textContent = '0';
      document.getElementById('audioTracks').textContent = '0';
      document.getElementById('resolution').textContent = '-';
    }
  }

  updateStatus(status, text) {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    
    statusDot.className = `status-dot ${status}`;
    statusText.textContent = text;
  }

  updateWSStatus(status) {
    document.getElementById('wsStatus').textContent = status;
  }

  updateClientInfo() {
    document.getElementById('clientId').textContent = this.clientId || '-';
    document.getElementById('currentRoom').textContent = this.currentRoom || '-';
    document.getElementById('peerState').textContent = this.pc ? this.pc.connectionState : '-';
  }

  updateUI() {
    const connectBtn = document.getElementById('connectBtn');
    const disconnectBtn = document.getElementById('disconnectBtn');
    const roomInput = document.getElementById('roomInput');
    
    connectBtn.disabled = this.isConnected;
    disconnectBtn.disabled = !this.isConnected;
    roomInput.disabled = this.isConnected;
    
    this.updateClientInfo();
  }

  toggleFullscreen() {
    if (!document.fullscreenElement) {
      this.remoteVideo.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  }

  log(message, type = 'info') {
    const logContainer = document.getElementById('logContainer');
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
    
    // Keep only last 100 entries
    while (logContainer.children.length > 100) {
      logContainer.removeChild(logContainer.firstChild);
    }
    
    console.log(`[${type.toUpperCase()}] ${message}`);
  }
}

// Initialize client when page loads
document.addEventListener('DOMContentLoaded', () => {
  new WebRTCClient();
});
