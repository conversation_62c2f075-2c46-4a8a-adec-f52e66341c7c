{"name": "webrtc-signaling-server", "version": "1.0.0", "description": "WebSocket signaling server for Chrome extension tab sharing", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "demo": "node start-demo.js"}, "dependencies": {"ws": "^8.14.2", "express": "^4.18.2", "cors": "^2.8.5", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["webrtc", "signaling", "websocket", "chrome-extension"], "author": "Tab Screen Share Extension", "license": "MIT"}