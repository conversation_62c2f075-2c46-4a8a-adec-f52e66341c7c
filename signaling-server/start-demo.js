#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting WebRTC Tab Screen Share Demo');
console.log('=====================================');

// Check if dependencies are installed
const fs = require('fs');
const packagePath = path.join(__dirname, 'package.json');
const nodeModulesPath = path.join(__dirname, 'node_modules');

if (!fs.existsSync(nodeModulesPath)) {
  console.log('📦 Installing dependencies...');
  const install = spawn('npm', ['install'], { 
    cwd: __dirname, 
    stdio: 'inherit',
    shell: true 
  });
  
  install.on('close', (code) => {
    if (code === 0) {
      startServer();
    } else {
      console.error('❌ Failed to install dependencies');
      process.exit(1);
    }
  });
} else {
  startServer();
}

function startServer() {
  console.log('🌐 Starting signaling server...');
  
  const server = spawn('node', ['server.js'], { 
    cwd: __dirname, 
    stdio: 'inherit' 
  });
  
  server.on('close', (code) => {
    console.log(`\n📡 Signaling server stopped with code ${code}`);
  });
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down demo...');
    server.kill('SIGINT');
    process.exit(0);
  });
  
  // Print instructions after a short delay
  setTimeout(() => {
    console.log('\n📋 Demo Instructions:');
    console.log('====================');
    console.log('1. Open Chrome and go to chrome://extensions/');
    console.log('2. Enable Developer mode and load the extension from tab-screen-share-extension/');
    console.log('3. Open http://localhost:3000 in a new browser window');
    console.log('4. Click "Connect" in the web client');
    console.log('5. Go to any regular website and click the extension icon');
    console.log('6. Click "Start Capture" then "Start WebRTC Stream"');
    console.log('7. Watch the stream appear in the web client!');
    console.log('\n🔗 Web Client: http://localhost:3000');
    console.log('🔗 Server Status: http://localhost:3000/status');
    console.log('\nPress Ctrl+C to stop the demo');
  }, 2000);
}
