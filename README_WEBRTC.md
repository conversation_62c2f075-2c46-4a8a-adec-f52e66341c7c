# WebRTC Tab Screen Share System

A complete WebRTC-based tab screen sharing solution consisting of a Chrome extension, signaling server, and web client for real-time streaming of browser tab content.

## 🎯 Features

- **Real-time Tab Capture**: Capture any browser tab content with audio and video
- **WebRTC Streaming**: Low-latency peer-to-peer streaming
- **Web Client Interface**: Professional web interface for viewing streams
- **Signaling Server**: WebSocket-based signaling for connection establishment
- **Chrome Extension**: Easy-to-use popup interface for stream control
- **Room-based Connections**: Support for multiple streaming rooms
- **Connection Management**: Automatic reconnection and error handling

## 🏗️ Architecture

```
┌─────────────────┐    WebSocket     ┌─────────────────┐
│  Chrome         │◄────Signaling───►│  Signaling      │
│  Extension      │                  │  Server         │
│                 │                  │  (Node.js)      │
└─────────────────┘                  └─────────────────┘
         │                                    ▲
         │                                    │
         │ WebRTC Stream                      │ WebSocket
         │ (P2P)                              │ Signaling
         ▼                                    │
┌─────────────────┐                  ┌─────────────────┐
│  Web Client     │◄─────────────────┤  Web Client     │
│  (<PERSON>rowser)      │                  │  Interface      │
└─────────────────┘                  └─────────────────┘
```

## 🚀 Quick Start

### 1. Start the Demo

```bash
cd signaling-server
npm run demo
```

This will:
- Install dependencies automatically
- Start the signaling server
- Display setup instructions

### 2. Load the Chrome Extension

1. Open Chrome → `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked"
4. Select the `tab-screen-share-extension` folder

### 3. Open the Web Client

1. Navigate to `http://localhost:3000`
2. Click "Connect" to join the default room

### 4. Start Streaming

1. Go to any website in a new tab
2. Click the extension icon
3. Click "Start Capture"
4. Click "Start WebRTC Stream"
5. View the stream in the web client!

## 📁 Project Structure

```
├── tab-screen-share-extension/     # Chrome Extension
│   ├── manifest.json              # Extension manifest
│   ├── background.js               # Service worker
│   ├── popup.html/js/css          # Extension popup UI
│   ├── offscreen.html/js          # Offscreen document for media capture
│   └── webrtc-manager.js          # WebRTC connection management
│
├── signaling-server/               # WebSocket Signaling Server
│   ├── server.js                  # Main server file
│   ├── package.json               # Dependencies
│   ├── start-demo.js              # Demo startup script
│   └── public/                    # Web client files
│       ├── index.html             # Web client interface
│       └── client.js              # Web client logic
│
├── WEBRTC_SETUP_GUIDE.md          # Detailed setup guide
└── README_WEBRTC.md               # This file
```

## 🔧 Technical Details

### Chrome Extension Components

- **Background Script**: Manages tab capture and WebRTC connections
- **Popup Interface**: User controls for starting/stopping capture and streaming
- **Offscreen Document**: Handles getUserMedia calls in Chrome MV3
- **WebRTC Manager**: Manages peer connections and signaling

### Signaling Server

- **WebSocket Server**: Handles real-time signaling between peers
- **Room Management**: Supports multiple streaming rooms
- **Client Registration**: Tracks extension and web client connections
- **Message Routing**: Routes offers, answers, and ICE candidates

### Web Client

- **Real-time Video**: Displays incoming WebRTC streams
- **Connection Status**: Shows connection and stream information
- **User Controls**: Connect/disconnect and fullscreen controls
- **Activity Logging**: Real-time logging of connection events

## 🛠️ Development

### Prerequisites

- Node.js 14+
- Chrome 116+
- Basic WebRTC knowledge

### Local Development

1. **Extension Development**:
   ```bash
   # Make changes to extension files
   # Reload extension in chrome://extensions/
   ```

2. **Server Development**:
   ```bash
   cd signaling-server
   npm run dev  # Uses nodemon for auto-restart
   ```

3. **Testing**:
   - Use `chrome://webrtc-internals/` for WebRTC debugging
   - Check browser console for detailed logs
   - Monitor signaling server logs

### Key APIs Used

- **Chrome Extensions API**: `chrome.tabCapture`, `chrome.runtime`
- **WebRTC API**: `RTCPeerConnection`, `getUserMedia`
- **WebSocket API**: Real-time signaling communication

## 🔒 Security Considerations

- **HTTPS Required**: Production deployment requires HTTPS
- **Origin Validation**: Validate message origins in production
- **Authentication**: Add user authentication for production use
- **Room Security**: Implement access controls for streaming rooms

## 🚀 Production Deployment

### Signaling Server

```bash
# Deploy to cloud platform (Heroku, AWS, etc.)
cd signaling-server
npm install --production
npm start
```

### Extension Distribution

1. Package extension for Chrome Web Store
2. Update signaling server URLs in production
3. Add proper CSP headers

### Web Client

1. Deploy static files to CDN
2. Configure HTTPS endpoints
3. Add analytics and monitoring

## 📊 Performance

- **Latency**: ~100-300ms depending on network conditions
- **Quality**: Supports up to 1080p video capture
- **CPU Usage**: Moderate impact during active capture
- **Memory**: ~50-100MB per active stream

## 🐛 Troubleshooting

### Common Issues

1. **"Cannot capture system pages"**
   - Solution: Use regular websites, not chrome:// pages

2. **WebRTC connection fails**
   - Check STUN/TURN server configuration
   - Verify firewall settings

3. **No video in web client**
   - Ensure extension started WebRTC streaming
   - Check browser console for errors

4. **Signaling connection fails**
   - Verify server is running on correct port
   - Check WebSocket connection in browser dev tools

### Debug Tools

- `chrome://webrtc-internals/` - WebRTC connection details
- Browser console - JavaScript errors and logs
- Network tab - WebSocket connection status
- Extension console - Extension-specific logs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Chrome Extensions API documentation
- WebRTC specification and examples
- Node.js WebSocket libraries
- Open source WebRTC community

---

**Ready to start streaming?** Run `npm run demo` in the signaling-server directory and follow the setup instructions!
