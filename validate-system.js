#!/usr/bin/env node

/**
 * System Validation Script
 * Validates the entire Chrome extension and automation system
 */

import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SystemValidator {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.successes = [];
  }

  log(message, type = 'info') {
    const prefix = {
      'success': '✅',
      'warning': '⚠️',
      'error': '❌',
      'info': 'ℹ️'
    }[type] || 'ℹ️';
    
    console.log(`${prefix} ${message}`);
    
    if (type === 'error') {
      this.issues.push(message);
    } else if (type === 'warning') {
      this.warnings.push(message);
    } else if (type === 'success') {
      this.successes.push(message);
    }
  }

  /**
   * Validate extension files
   */
  validateExtensionFiles() {
    this.log('Validating extension files...', 'info');
    
    const extensionPath = path.join(__dirname, 'tab-screen-share-extension');
    
    if (!fs.existsSync(extensionPath)) {
      this.log('Extension directory not found', 'error');
      return false;
    }
    
    const requiredFiles = [
      { file: 'manifest.json', critical: true },
      { file: 'background.js', critical: true },
      { file: 'popup.html', critical: true },
      { file: 'popup.js', critical: true },
      { file: 'popup.css', critical: false },
      { file: 'offscreen.html', critical: true },
      { file: 'offscreen.js', critical: true },
      { file: 'content.js', critical: false },
      { file: 'test-page.html', critical: false }
    ];
    
    let allCriticalFound = true;
    
    for (const { file, critical } of requiredFiles) {
      const filePath = path.join(extensionPath, file);
      if (fs.existsSync(filePath)) {
        this.log(`Extension file found: ${file}`, 'success');
      } else {
        const level = critical ? 'error' : 'warning';
        this.log(`Extension file missing: ${file}`, level);
        if (critical) allCriticalFound = false;
      }
    }
    
    return allCriticalFound;
  }

  /**
   * Validate manifest.json
   */
  validateManifest() {
    this.log('Validating manifest.json...', 'info');
    
    const manifestPath = path.join(__dirname, 'tab-screen-share-extension', 'manifest.json');
    
    if (!fs.existsSync(manifestPath)) {
      this.log('manifest.json not found', 'error');
      return false;
    }
    
    try {
      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
      
      // Check required fields
      const requiredFields = [
        'manifest_version',
        'name',
        'version',
        'permissions',
        'background',
        'action'
      ];
      
      for (const field of requiredFields) {
        if (!(field in manifest)) {
          this.log(`Manifest missing required field: ${field}`, 'error');
          return false;
        }
      }
      
      // Check manifest version
      if (manifest.manifest_version !== 3) {
        this.log(`Expected manifest version 3, got ${manifest.manifest_version}`, 'warning');
      }
      
      // Check permissions
      const requiredPermissions = ['activeTab', 'tabs', 'tabCapture', 'offscreen'];
      const missingPermissions = requiredPermissions.filter(
        perm => !manifest.permissions.includes(perm)
      );
      
      if (missingPermissions.length > 0) {
        this.log(`Missing permissions: ${missingPermissions.join(', ')}`, 'error');
        return false;
      }
      
      this.log('Manifest validation passed', 'success');
      return true;
      
    } catch (error) {
      this.log(`Manifest parse error: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * Validate signaling server
   */
  validateSignalingServer() {
    this.log('Validating signaling server...', 'info');
    
    const serverPath = path.join(__dirname, 'signaling-server');
    
    if (!fs.existsSync(serverPath)) {
      this.log('Signaling server directory not found', 'error');
      return false;
    }
    
    const requiredFiles = ['server.js', 'package.json'];
    
    for (const file of requiredFiles) {
      const filePath = path.join(serverPath, file);
      if (!fs.existsSync(filePath)) {
        this.log(`Signaling server file missing: ${file}`, 'error');
        return false;
      }
    }
    
    // Check package.json
    try {
      const packagePath = path.join(serverPath, 'package.json');
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      
      const requiredDeps = ['ws', 'express'];
      const missingDeps = requiredDeps.filter(
        dep => !(dep in (packageJson.dependencies || {}))
      );
      
      if (missingDeps.length > 0) {
        this.log(`Signaling server missing dependencies: ${missingDeps.join(', ')}`, 'warning');
      }
      
    } catch (error) {
      this.log(`Signaling server package.json error: ${error.message}`, 'warning');
    }
    
    this.log('Signaling server validation passed', 'success');
    return true;
  }

  /**
   * Validate automation scripts
   */
  validateAutomationScripts() {
    this.log('Validating automation scripts...', 'info');
    
    const scripts = [
      'automation-script.js',
      'automation-cli.js',
      'demo-automation.js',
      'test-automation.js',
      'debug-automation.js'
    ];
    
    let allFound = true;
    
    for (const script of scripts) {
      const scriptPath = path.join(__dirname, script);
      if (fs.existsSync(scriptPath)) {
        this.log(`Automation script found: ${script}`, 'success');
      } else {
        this.log(`Automation script missing: ${script}`, 'warning');
        allFound = false;
      }
    }
    
    return allFound;
  }

  /**
   * Test signaling server startup
   */
  async testSignalingServer() {
    this.log('Testing signaling server startup...', 'info');
    
    return new Promise((resolve) => {
      const serverPath = path.join(__dirname, 'signaling-server');
      
      const server = spawn('npm', ['start'], {
        cwd: serverPath,
        stdio: 'pipe'
      });
      
      let started = false;
      const timeout = setTimeout(() => {
        if (!started) {
          server.kill();
          this.log('Signaling server startup timeout', 'error');
          resolve(false);
        }
      }, 10000);
      
      server.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('Server running') || output.includes('listening')) {
          started = true;
          clearTimeout(timeout);
          server.kill();
          this.log('Signaling server started successfully', 'success');
          resolve(true);
        }
      });
      
      server.stderr.on('data', (data) => {
        const error = data.toString();
        if (error.includes('EADDRINUSE')) {
          started = true;
          clearTimeout(timeout);
          server.kill();
          this.log('Signaling server port already in use (may be running)', 'warning');
          resolve(true);
        }
      });
      
      server.on('error', (error) => {
        clearTimeout(timeout);
        this.log(`Signaling server error: ${error.message}`, 'error');
        resolve(false);
      });
    });
  }

  /**
   * Check Node.js and npm versions
   */
  checkNodeEnvironment() {
    this.log('Checking Node.js environment...', 'info');
    
    const nodeVersion = process.version;
    this.log(`Node.js version: ${nodeVersion}`, 'info');
    
    // Check if Node version is compatible
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 16) {
      this.log('Node.js version 16+ recommended for ES modules', 'warning');
    }
    
    // Check npm
    try {
      const { execSync } = require('child_process');
      const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
      this.log(`npm version: ${npmVersion}`, 'info');
    } catch (error) {
      this.log('npm not found or not working', 'warning');
    }
    
    return true;
  }

  /**
   * Run full system validation
   */
  async runValidation() {
    console.log('🔍 System Validation');
    console.log('=' .repeat(20));
    
    const checks = [
      () => this.checkNodeEnvironment(),
      () => this.validateExtensionFiles(),
      () => this.validateManifest(),
      () => this.validateSignalingServer(),
      () => this.validateAutomationScripts(),
      () => this.testSignalingServer()
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const check of checks) {
      try {
        const result = await check();
        if (result) {
          passed++;
        } else {
          failed++;
        }
      } catch (error) {
        this.log(`Validation error: ${error.message}`, 'error');
        failed++;
      }
    }
    
    this.printSummary(passed, failed);
    
    return {
      passed,
      failed,
      issues: this.issues,
      warnings: this.warnings,
      successes: this.successes
    };
  }

  /**
   * Print validation summary
   */
  printSummary(passed, failed) {
    console.log('\n📊 Validation Summary');
    console.log('=' .repeat(22));
    console.log(`✅ Checks passed: ${passed}`);
    console.log(`❌ Checks failed: ${failed}`);
    console.log(`⚠️ Warnings: ${this.warnings.length}`);
    
    if (this.issues.length > 0) {
      console.log('\n❌ Issues found:');
      this.issues.forEach(issue => console.log(`  - ${issue}`));
    }
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️ Warnings:');
      this.warnings.forEach(warning => console.log(`  - ${warning}`));
    }
    
    if (failed === 0) {
      console.log('\n🎉 System validation passed! Ready to run automation.');
    } else {
      console.log('\n🔧 Please fix the issues above before running automation.');
    }
  }
}

// Run validation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new SystemValidator();
  validator.runValidation();
}

export default SystemValidator;
