#!/usr/bin/env node

/**
 * Demo Script for Chrome Extension Automation
 * Demonstrates automated control of tab screen sharing and WebRTC streaming
 */

import ExtensionAutomation from "./automation-script.js";

class AutomationDemo {
  constructor() {
    this.automation = new ExtensionAutomation();
  }

  /**
   * Run a complete automation demo
   */
  async runDemo() {
    console.log("🎬 Starting Chrome Extension Automation Demo");
    console.log("=".repeat(50));

    try {
      // Step 1: Initialize
      console.log("\n📋 Step 1: Initializing automation system...");
      await this.automation.launchChrome();
      await this.automation.getExtensionId();
      await this.automation.setupTestPage();

      await this.wait(2000);

      // Step 2: Check initial status
      console.log("\n📋 Step 2: Checking initial status...");
      await this.automation.getStatus();

      await this.wait(2000);

      // Step 3: Start capture
      console.log("\n📋 Step 3: Starting tab capture...");
      const captureSuccess = await this.automation.startCapture();
      r;
      if (!captureSuccess) {
        throw new Error("Failed to start capture");
      }

      await this.wait(3000);

      // Step 4: Check status after capture
      console.log("\n📋 Step 4: Checking status after capture...");
      await this.automation.getStatus();

      await this.wait(2000);

      // Step 5: Start WebRTC streaming
      console.log("\n📋 Step 5: Starting WebRTC streaming...");
      const streamSuccess = await this.automation.startWebRTCStream();

      if (!streamSuccess) {
        throw new Error("Failed to start WebRTC streaming");
      }

      await this.wait(3000);

      // Step 6: Check final status
      console.log("\n📋 Step 6: Checking streaming status...");
      const finalStatus = await this.automation.getStatus();

      if (finalStatus.streaming) {
        console.log("🎉 SUCCESS: Streaming is active!");
        console.log("📺 You can view the stream at: http://localhost:3000");
      }

      // Step 7: Demo streaming for a while
      console.log("\n📋 Step 7: Streaming demo (30 seconds)...");
      console.log("⏰ Streaming will continue for 30 seconds...");

      for (let i = 30; i > 0; i--) {
        process.stdout.write(`\r⏱️  Streaming... ${i} seconds remaining`);
        await this.wait(1000);
      }
      console.log("\n");

      // Step 8: Stop streaming
      console.log("\n📋 Step 8: Stopping streaming...");
      await this.automation.stopStreaming();

      await this.wait(2000);

      // Step 9: Final status check
      console.log("\n📋 Step 9: Final status check...");
      await this.automation.getStatus();

      console.log("\n🎉 Demo completed successfully!");
    } catch (error) {
      console.error("\n❌ Demo failed:", error.message);
      throw error;
    }
  }

  /**
   * Run interactive demo with user prompts
   */
  async runInteractiveDemo() {
    const readline = await import("readline");
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    const prompt = (question) => {
      return new Promise((resolve) => {
        rl.question(question, resolve);
      });
    };

    console.log("🎮 Interactive Chrome Extension Demo");
    console.log("=".repeat(40));

    try {
      // Initialize
      console.log("\n🔧 Initializing...");
      await this.automation.launchChrome();
      await this.automation.getExtensionId();
      await this.automation.setupTestPage();

      await prompt(
        "\n✅ Chrome launched with extension. Press Enter to continue..."
      );

      // Start capture
      console.log("\n🎥 Starting tab capture...");
      await this.automation.startCapture();

      await prompt(
        "\n✅ Tab capture started. Press Enter to start WebRTC streaming..."
      );

      // Start streaming
      console.log("\n🌐 Starting WebRTC streaming...");
      await this.automation.startWebRTCStream();

      console.log("\n🎉 Streaming is now active!");
      console.log(
        "📺 Open http://localhost:3000 in another browser to view the stream"
      );

      await prompt("\n⏸️  Press Enter when you want to stop streaming...");

      // Stop streaming
      console.log("\n🛑 Stopping streaming...");
      await this.automation.stopStreaming();

      console.log("\n✅ Demo completed!");
    } catch (error) {
      console.error("\n❌ Interactive demo failed:", error.message);
    } finally {
      rl.close();
    }
  }

  /**
   * Test all automation functions
   */
  async runFunctionTests() {
    console.log("🧪 Running Function Tests");
    console.log("=".repeat(30));

    const tests = [
      {
        name: "Launch Chrome",
        fn: () => this.automation.launchChrome(),
      },
      {
        name: "Get Extension ID",
        fn: () => this.automation.getExtensionId(),
      },
      {
        name: "Setup Test Page",
        fn: () => this.automation.setupTestPage(),
      },
      {
        name: "Get Initial Status",
        fn: () => this.automation.getStatus(),
      },
      {
        name: "Start Capture",
        fn: () => this.automation.startCapture(),
      },
      {
        name: "Get Status After Capture",
        fn: () => this.automation.getStatus(),
      },
      {
        name: "Start WebRTC Stream",
        fn: () => this.automation.startWebRTCStream(),
      },
      {
        name: "Get Status While Streaming",
        fn: () => this.automation.getStatus(),
      },
      {
        name: "Stop Streaming",
        fn: () => this.automation.stopStreaming(),
      },
      {
        name: "Get Final Status",
        fn: () => this.automation.getStatus(),
      },
    ];

    let passed = 0;
    let failed = 0;

    for (const test of tests) {
      try {
        console.log(`\n🧪 Testing: ${test.name}...`);
        await test.fn();
        console.log(`✅ ${test.name} - PASSED`);
        passed++;

        // Small delay between tests
        await this.wait(1000);
      } catch (error) {
        console.error(`❌ ${test.name} - FAILED: ${error.message}`);
        failed++;
      }
    }

    console.log("\n📊 Test Results:");
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(
      `📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`
    );
  }

  /**
   * Cleanup
   */
  async cleanup() {
    await this.automation.cleanup();
  }

  /**
   * Wait helper
   */
  async wait(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const mode = args[0] || "auto";

  const demo = new AutomationDemo();

  // Handle cleanup on exit
  process.on("SIGINT", async () => {
    console.log("\n🛑 Received interrupt signal");
    await demo.cleanup();
    process.exit(0);
  });

  try {
    switch (mode) {
      case "auto":
        await demo.runDemo();
        break;

      case "interactive":
        await demo.runInteractiveDemo();
        break;

      case "test":
        await demo.runFunctionTests();
        break;

      default:
        console.log("🤖 Chrome Extension Automation Demo");
        console.log("");
        console.log("Usage: node demo-automation.js [mode]");
        console.log("");
        console.log("Modes:");
        console.log("  auto         - Run automated demo (default)");
        console.log("  interactive  - Run interactive demo with prompts");
        console.log("  test         - Run function tests");
        console.log("");
        return;
    }
  } catch (error) {
    console.error("❌ Demo Error:", error.message);
  } finally {
    await demo.cleanup();
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default AutomationDemo;
