#!/usr/bin/env node

/**
 * Manual test script - opens Chrome and waits for manual testing
 */

import ExtensionAutomation from "./automation-script.js";

class ManualTester {
  constructor() {
    this.automation = new ExtensionAutomation();
  }

  async startManualTest() {
    console.log("🔧 Starting manual test environment...");
    
    try {
      // Launch Chrome with extension
      await this.automation.launchChrome();
      await this.automation.getExtensionId();
      await this.automation.setupTestPage();
      
      console.log("\n✅ Chrome is now running with the extension loaded!");
      console.log("📋 Manual Testing Instructions:");
      console.log("================================");
      console.log("1. 🌐 The example.com page should be open");
      console.log("2. 🔧 Click the extension icon in the toolbar");
      console.log("3. 🎥 Click 'Start Capture' button");
      console.log("4. 👀 Check the browser console (F12) for debug logs");
      console.log("5. 📊 Look for the debug messages we added:");
      console.log("   - 🔍 Querying for active tab...");
      console.log("   - 📋 Active tabs in current window:");
      console.log("   - 📤 Sending startTabCapture message...");
      console.log("   - 🎉 Received streamReady message:");
      console.log("");
      console.log("🌐 Web client is available at: http://localhost:3000");
      console.log("");
      console.log("⏰ Browser will stay open for 5 minutes for manual testing");
      console.log("💡 Press Ctrl+C to close early");
      
      // Wait for 5 minutes
      await new Promise(resolve => setTimeout(resolve, 5 * 60 * 1000));
      
      console.log("\n⏰ 5 minutes elapsed, closing browser...");
      
    } catch (error) {
      console.error("❌ Manual test setup failed:", error.message);
    } finally {
      await this.cleanup();
    }
  }
  
  async cleanup() {
    await this.automation.cleanup();
  }
}

// Main execution
async function main() {
  const tester = new ManualTester();
  
  // Handle cleanup on exit
  process.on("SIGINT", async () => {
    console.log("\n🛑 Cleaning up...");
    await tester.cleanup();
    process.exit(0);
  });
  
  await tester.startManualTest();
}

main().catch(console.error);
