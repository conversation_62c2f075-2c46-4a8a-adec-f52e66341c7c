# WebRTC Tab Screen Share - Complete Setup Guide

This guide provides step-by-step instructions for setting up and testing the complete WebRTC tab screen sharing system.

## System Overview

The system consists of three main components:

1. **Chrome Extension** - Captures tab content and streams via WebRTC
2. **Signaling Server** - WebSocket server for WebRTC signaling
3. **Web Client** - Browser-based receiver for viewing streams

## Prerequisites

- Node.js (v14 or higher)
- Chrome browser (v116 or higher)
- Basic understanding of WebRTC concepts

## Installation Steps

### 1. Install Signaling Server Dependencies

```bash
cd signaling-server
npm install
```

### 2. Load Chrome Extension

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked"
4. Select the `tab-screen-share-extension` directory
5. Note the extension ID for later reference

### 3. Start the Signaling Server

```bash
cd signaling-server
npm start
```

The server will start on:
- WebSocket: `ws://localhost:3001`
- HTTP: `http://localhost:3000`

## Testing the Complete System

### Step 1: Start the Signaling Server

```bash
cd signaling-server
npm start
```

You should see:
```
WebSocket signaling server running on port 3001
HTTP server running on port 3000
```

### Step 2: Open the Web Client

1. Open a new browser window/tab
2. Navigate to `http://localhost:3000`
3. You should see the "Tab Screen Share - Web Client" interface
4. Click "Connect" to join the default room
5. Status should change to "Connected"

### Step 3: Test Tab Capture

1. Open another browser tab with any regular website (not chrome:// pages)
2. Click the Chrome extension icon in the toolbar
3. In the popup, click "Start Capture"
4. You should see:
   - Status changes to "Active"
   - Red recording indicator appears in the tab
   - Extension logs show "Tab capture started successfully"

### Step 4: Start WebRTC Streaming

1. In the extension popup, click "Start WebRTC Stream"
2. The extension will:
   - Connect to the signaling server
   - Create a WebRTC peer connection
   - Send stream offer to connected web clients

### Step 5: Verify Stream Reception

1. In the web client window, you should see:
   - "Web client joined room" message in logs
   - "Received offer from extension" message
   - Video stream appears in the main viewing area
   - Stream info updates (video/audio tracks, resolution)

## Troubleshooting

### Extension Issues

**Problem**: "Cannot capture system pages" error
- **Solution**: Navigate to a regular website (not chrome://, file://, or extension pages)

**Problem**: Extension popup shows "Not Capturing" after clicking start
- **Solution**: Check browser console for errors, ensure tab is capturable

**Problem**: WebRTC button is disabled
- **Solution**: Start tab capture first, WebRTC requires an active capture

### Signaling Server Issues

**Problem**: Connection refused to localhost:3001
- **Solution**: Ensure signaling server is running with `npm start`

**Problem**: CORS errors in browser console
- **Solution**: Server includes CORS headers, check if server started properly

### Web Client Issues

**Problem**: "WebSocket connection failed"
- **Solution**: Verify signaling server is running on port 3001

**Problem**: No video stream appears
- **Solution**: Check that extension has started WebRTC streaming

**Problem**: "Failed to create offer" errors
- **Solution**: Ensure both extension and client are in the same room

## Advanced Configuration

### Custom Room Names

1. In the web client, change the room ID before connecting
2. Extension automatically joins "default-room"
3. To use custom rooms, modify the extension's WebRTC manager

### Different Signaling Server

1. Update the WebRTC manager in `webrtc-manager.js`:
   ```javascript
   setupSignalingChannel('ws://your-server:port')
   ```

### STUN/TURN Servers

1. Modify the WebRTC configuration in both extension and web client:
   ```javascript
   const configuration = {
     iceServers: [
       { urls: 'stun:your-stun-server.com:19302' },
       { 
         urls: 'turn:your-turn-server.com:3478',
         username: 'user',
         credential: 'pass'
       }
     ]
   };
   ```

## API Reference

### Extension Messages

- `startTabCapture` - Begin capturing current tab
- `stopTabCapture` - Stop tab capture
- `startWebRTCStream` - Start WebRTC streaming
- `stopWebRTCStream` - Stop WebRTC streaming
- `getCaptureStatus` - Get current capture status

### Signaling Messages

- `register` - Register client type (extension/client)
- `join-room` - Join a signaling room
- `offer` - WebRTC offer
- `answer` - WebRTC answer
- `ice-candidate` - ICE candidate exchange
- `start-stream` - Notify stream availability
- `stop-stream` - Notify stream stopped

## Security Considerations

1. **HTTPS Required**: For production, use HTTPS for both signaling and web client
2. **Authentication**: Add authentication to signaling server for production use
3. **Room Security**: Implement room passwords or access controls
4. **TURN Server**: Use authenticated TURN servers for NAT traversal

## Performance Tips

1. **Video Quality**: Adjust capture constraints in offscreen.js
2. **Network**: Monitor WebRTC stats for connection quality
3. **CPU Usage**: Tab capture can be CPU intensive
4. **Memory**: Clean up streams properly to prevent memory leaks

## Next Steps

1. **Production Deployment**: Deploy signaling server to cloud platform
2. **Mobile Support**: Test with mobile browsers
3. **Recording**: Add server-side recording capabilities
4. **Multiple Streams**: Support multiple simultaneous streams
5. **Screen Sharing**: Extend to full screen capture

## Support

For issues and questions:
1. Check browser console for error messages
2. Verify all components are running
3. Test with simple websites first
4. Check network connectivity between components

## File Structure

```
├── tab-screen-share-extension/     # Chrome extension
│   ├── manifest.json
│   ├── background.js
│   ├── popup.js
│   ├── offscreen.js
│   └── webrtc-manager.js
├── signaling-server/               # WebSocket signaling server
│   ├── server.js
│   ├── package.json
│   └── public/
│       ├── index.html
│       └── client.js
└── WEBRTC_SETUP_GUIDE.md          # This guide
```

This completes the WebRTC tab screen sharing setup. The system provides a foundation for real-time tab content streaming between Chrome extensions and web clients.
