{"name": "chrome-extension-automation", "version": "1.0.0", "description": "Puppeteer automation for Chrome tab screen sharing extension", "type": "module", "main": "automation-script.js", "bin": {"extension-automation": "./automation-cli.js"}, "scripts": {"setup": "node setup-automation.js", "install-deps": "npm install", "start": "node automation-cli.js interactive", "init": "node automation-cli.js init", "start-streaming": "node automation-cli.js start", "stop-streaming": "node automation-cli.js stop", "status": "node automation-cli.js status", "demo": "node demo-automation.js", "demo-interactive": "node demo-automation.js interactive", "test": "node demo-automation.js test"}, "dependencies": {"puppeteer": "^21.5.0"}, "devDependencies": {}, "keywords": ["puppeteer", "chrome-extension", "automation", "webrtc", "screen-sharing"], "author": "Extension Automation", "license": "MIT"}