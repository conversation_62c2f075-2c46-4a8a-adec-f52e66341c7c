#!/usr/bin/env node

/**
 * Manual Test Guide and Interactive Verification
 * Provides step-by-step manual testing with automation assistance
 */

import ExtensionAutomation from './automation-script.js';
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ManualTestGuide {
  constructor() {
    this.automation = new ExtensionAutomation();
    this.signalingServer = null;
  }

  /**
   * Start signaling server
   */
  async startSignalingServer() {
    console.log('🚀 Starting signaling server...');
    
    const serverPath = path.join(__dirname, 'signaling-server');
    
    this.signalingServer = spawn('npm', ['start'], {
      cwd: serverPath,
      stdio: 'pipe',
      detached: false
    });

    return new Promise((resolve) => {
      let started = false;
      const timeout = setTimeout(() => {
        if (!started) {
          console.log('✅ Signaling server should be starting...');
          resolve();
        }
      }, 5000);

      this.signalingServer.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('Server running') || output.includes('listening')) {
          started = true;
          clearTimeout(timeout);
          console.log('✅ Signaling server started');
          resolve();
        }
      });

      this.signalingServer.stderr.on('data', (data) => {
        const error = data.toString();
        if (error.includes('EADDRINUSE')) {
          started = true;
          clearTimeout(timeout);
          console.log('✅ Signaling server already running');
          resolve();
        }
      });
    });
  }

  /**
   * Setup automation environment
   */
  async setupEnvironment() {
    console.log('\n🔧 Setting up test environment...');
    
    // Start signaling server
    await this.startSignalingServer();
    
    // Launch Chrome
    console.log('🚀 Launching Chrome with extension...');
    await this.automation.launchChrome();
    
    // Get extension ID
    await this.automation.getExtensionId();
    console.log(`✅ Extension ID: ${this.automation.extensionId}`);
    
    // Setup test page
    await this.automation.setupTestPage();
    console.log('✅ Test page ready');
    
    console.log('\n✅ Environment setup complete!');
  }

  /**
   * Manual test step 1: Basic extension functionality
   */
  async testStep1() {
    console.log('\n📋 STEP 1: Basic Extension Functionality');
    console.log('=' .repeat(45));
    
    console.log('1. Opening extension popup...');
    const popupPage = await this.automation.openExtensionPopup();
    
    console.log('2. Checking popup elements...');
    const elements = [
      '#startCaptureBtn',
      '#stopCaptureBtn', 
      '#startWebRTCBtn',
      '#stopWebRTCBtn'
    ];
    
    for (const selector of elements) {
      const element = await popupPage.$(selector);
      const exists = element !== null;
      console.log(`   ${selector}: ${exists ? '✅' : '❌'}`);
    }
    
    console.log('\n🔍 MANUAL CHECK:');
    console.log('   - Can you see the extension popup window?');
    console.log('   - Are all buttons visible?');
    console.log('   - Does the "Start Capture" button appear enabled?');
    
    await this.waitForUser('\nPress Enter when you have verified the popup...');
    await popupPage.close();
  }

  /**
   * Manual test step 2: Tab capture
   */
  async testStep2() {
    console.log('\n📋 STEP 2: Tab Capture Testing');
    console.log('=' .repeat(35));
    
    console.log('1. Attempting to start tab capture...');
    const captureResult = await this.automation.startCapture();
    
    if (captureResult) {
      console.log('✅ Automation reports capture started');
    } else {
      console.log('❌ Automation reports capture failed');
    }
    
    console.log('\n🔍 MANUAL CHECK:');
    console.log('   - Do you see a red recording dot in the browser tab?');
    console.log('   - Did Chrome show a "sharing screen" notification?');
    console.log('   - Open the extension popup - is "Start Capture" disabled?');
    console.log('   - Is "Stop Capture" enabled?');
    
    await this.waitForUser('\nPress Enter after checking capture indicators...');
    
    // Check status
    const status = await this.automation.getStatus();
    console.log(`📊 Status check: ${JSON.stringify(status)}`);
  }

  /**
   * Manual test step 3: WebRTC streaming
   */
  async testStep3() {
    console.log('\n📋 STEP 3: WebRTC Streaming Testing');
    console.log('=' .repeat(40));
    
    console.log('1. Opening web client in new tab...');
    console.log('   🌐 Navigate to: http://localhost:3000');
    
    await this.waitForUser('\nPress Enter after opening the web client...');
    
    console.log('2. Attempting to start WebRTC streaming...');
    try {
      const streamResult = await this.automation.startWebRTCStream();
      
      if (streamResult) {
        console.log('✅ Automation reports WebRTC started');
      } else {
        console.log('❌ Automation reports WebRTC failed');
      }
    } catch (error) {
      console.log(`❌ WebRTC error: ${error.message}`);
    }
    
    console.log('\n🔍 MANUAL CHECK:');
    console.log('   - In the web client, click "Connect"');
    console.log('   - Do you see the captured tab content in the web client?');
    console.log('   - Is the video stream live and updating?');
    console.log('   - Check extension popup - is "Start WebRTC" disabled?');
    console.log('   - Is "Stop WebRTC" enabled?');
    
    await this.waitForUser('\nPress Enter after verifying the stream...');
  }

  /**
   * Manual test step 4: Cleanup
   */
  async testStep4() {
    console.log('\n📋 STEP 4: Cleanup Testing');
    console.log('=' .repeat(30));
    
    console.log('1. Stopping streaming...');
    await this.automation.stopStreaming();
    
    console.log('\n🔍 MANUAL CHECK:');
    console.log('   - Did the video stream stop in the web client?');
    console.log('   - Did the red recording dot disappear from the tab?');
    console.log('   - Check extension popup - are buttons back to initial state?');
    
    await this.waitForUser('\nPress Enter after verifying cleanup...');
  }

  /**
   * Wait for user input
   */
  async waitForUser(message) {
    const readline = await import('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question(message, () => {
        rl.close();
        resolve();
      });
    });
  }

  /**
   * Run complete manual test
   */
  async runManualTest() {
    console.log('🧪 Manual Test Guide for Chrome Extension');
    console.log('=' .repeat(45));
    console.log('This guide will help you manually verify all functionality');
    console.log('while automation handles the technical setup.\n');

    try {
      await this.setupEnvironment();
      await this.testStep1();
      await this.testStep2();
      await this.testStep3();
      await this.testStep4();
      
      console.log('\n🎉 Manual testing complete!');
      console.log('\n📝 Test Summary:');
      console.log('   ✅ Environment setup');
      console.log('   ✅ Extension popup functionality');
      console.log('   ✅ Tab capture verification');
      console.log('   ✅ WebRTC streaming verification');
      console.log('   ✅ Cleanup verification');
      
      console.log('\n💡 If any step failed, check the console logs above for details.');
      
    } catch (error) {
      console.error(`❌ Manual test failed: ${error.message}`);
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Cleanup
   */
  async cleanup() {
    console.log('\n🧹 Cleaning up test environment...');
    
    await this.automation.cleanup();
    
    if (this.signalingServer) {
      this.signalingServer.kill();
    }
    
    console.log('✅ Cleanup complete');
  }
}

// Run manual test if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const testGuide = new ManualTestGuide();
  
  process.on('SIGINT', async () => {
    console.log('\n🛑 Manual test interrupted');
    await testGuide.cleanup();
    process.exit(0);
  });
  
  testGuide.runManualTest();
}

export default ManualTestGuide;
