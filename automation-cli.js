#!/usr/bin/env node

/**
 * CLI Interface for Chrome Extension Automation
 * Provides command-line control over the tab screen sharing extension
 */

import ExtensionAutomation from "./automation-script.js";
import { spawn } from "child_process";
import path from "path";
import { fileURLToPath } from "url";

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class AutomationCLI {
  constructor() {
    this.automation = new ExtensionAutomation();
    this.signalingServer = null;
    this.isInitialized = false;
  }

  /**
   * Check if signaling server is running
   */
  async checkSignalingServer() {
    try {
      const response = await fetch("http://localhost:3001/status");
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Start signaling server if not running
   */
  async ensureSignalingServer() {
    const isRunning = await this.checkSignalingServer();

    if (!isRunning) {
      console.log("🚀 Starting signaling server...");

      const serverPath = path.resolve(__dirname, "signaling-server");
      this.signalingServer = spawn("npm", ["start"], {
        cwd: serverPath,
        stdio: "pipe",
        detached: false,
      });

      // Wait for server to start
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(
            new Error("Signaling server failed to start within 10 seconds")
          );
        }, 10000);

        const checkServer = async () => {
          if (await this.checkSignalingServer()) {
            clearTimeout(timeout);
            console.log("✅ Signaling server started");
            resolve();
          } else {
            setTimeout(checkServer, 500);
          }
        };

        checkServer();
      });
    } else {
      console.log("✅ Signaling server already running");
    }
  }

  /**
   * Initialize the automation system
   */
  async initialize() {
    if (this.isInitialized) {
      console.log("ℹ️ Already initialized");
      return;
    }

    console.log("🔧 Initializing automation system...");

    try {
      // Ensure signaling server is running
      await this.ensureSignalingServer();

      // Launch Chrome with extension
      await this.automation.launchChrome();
      await this.automation.getExtensionId();
      await this.automation.setupTestPage();

      this.isInitialized = true;
      console.log("✅ Automation system initialized successfully");
    } catch (error) {
      console.error("❌ Failed to initialize:", error.message);
      throw error;
    }
  }

  /**
   * Start streaming (capture + WebRTC)
   */
  async startStreaming() {
    await this.ensureInitialized();

    console.log("🎬 Starting complete streaming pipeline...");

    try {
      // Start capture
      const captureSuccess = await this.automation.startCapture();
      if (!captureSuccess) {
        throw new Error("Failed to start capture");
      }

      // Start WebRTC streaming
      const streamSuccess = await this.automation.startWebRTCStream();
      if (!streamSuccess) {
        throw new Error("Failed to start WebRTC streaming");
      }

      console.log("🎉 Streaming started successfully!");
      console.log("📺 You can now view the stream at: http://localhost:3000");

      return true;
    } catch (error) {
      console.error("❌ Failed to start streaming:", error.message);
      return false;
    }
  }

  /**
   * Stop streaming
   */
  async stopStreaming() {
    await this.ensureInitialized();

    const success = await this.automation.stopStreaming();
    if (success) {
      console.log("✅ Streaming stopped successfully");
    }
    return success;
  }

  /**
   * Get current status
   */
  async getStatus() {
    await this.ensureInitialized();
    return await this.automation.getStatus();
  }

  /**
   * Ensure system is initialized
   */
  async ensureInitialized() {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  /**
   * Cleanup and shutdown
   */
  async cleanup() {
    console.log("🧹 Shutting down automation system...");

    if (this.automation) {
      await this.automation.cleanup();
    }

    if (this.signalingServer) {
      console.log("🛑 Stopping signaling server...");
      this.signalingServer.kill();
    }

    console.log("✅ Cleanup complete");
  }

  /**
   * Interactive mode
   */
  async interactive() {
    const readline = await import("readline");
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    console.log("\n🎮 Interactive Mode - Available Commands:");
    console.log("  start    - Start streaming");
    console.log("  stop     - Stop streaming");
    console.log("  status   - Get current status");
    console.log("  quit     - Exit interactive mode");
    console.log("");

    const askCommand = () => {
      rl.question("automation> ", async (command) => {
        const cmd = command.trim().toLowerCase();

        try {
          switch (cmd) {
            case "start":
              await this.startStreaming();
              break;

            case "stop":
              await this.stopStreaming();
              break;

            case "status":
              await this.getStatus();
              break;

            case "quit":
            case "exit":
              rl.close();
              return;

            default:
              console.log(
                "❓ Unknown command. Available: start, stop, status, quit"
              );
          }
        } catch (error) {
          console.error("❌ Command failed:", error.message);
        }

        askCommand();
      });
    };

    await this.initialize();
    askCommand();

    rl.on("close", async () => {
      await this.cleanup();
      process.exit(0);
    });
  }
}

// CLI Handler
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  const cli = new AutomationCLI();

  // Handle process termination
  process.on("SIGINT", async () => {
    console.log("\n🛑 Received interrupt signal");
    await cli.cleanup();
    process.exit(0);
  });

  try {
    switch (command) {
      case "init":
        await cli.initialize();
        break;

      case "start":
        await cli.startStreaming();
        break;

      case "stop":
        await cli.stopStreaming();
        break;

      case "status":
        await cli.getStatus();
        break;

      case "interactive":
      case "i":
        await cli.interactive();
        break;

      default:
        console.log("🤖 Chrome Extension Automation CLI");
        console.log("");
        console.log("Usage: node automation-cli.js <command>");
        console.log("");
        console.log("Commands:");
        console.log("  init         - Initialize the automation system");
        console.log("  start        - Start streaming (capture + WebRTC)");
        console.log("  stop         - Stop streaming");
        console.log("  status       - Get current streaming status");
        console.log("  interactive  - Enter interactive mode");
        console.log("");
        console.log("Examples:");
        console.log("  node automation-cli.js start");
        console.log("  node automation-cli.js interactive");
        break;
    }

    if (command && command !== "interactive") {
      await cli.cleanup();
    }
  } catch (error) {
    console.error("❌ CLI Error:", error.message);
    await cli.cleanup();
    process.exit(1);
  }
}

// Run CLI if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default AutomationCLI;
