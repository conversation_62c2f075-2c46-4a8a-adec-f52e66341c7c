#!/usr/bin/env node

/**
 * Setup Script for Chrome Extension Automation
 * Installs dependencies and verifies the automation environment
 */

import fs from "fs";
import path from "path";
import { spawn, execSync } from "child_process";
import { fileURLToPath } from "url";

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class AutomationSetup {
  constructor() {
    this.rootDir = __dirname;
    this.extensionDir = path.join(this.rootDir, "tab-screen-share-extension");
    this.signalingDir = path.join(this.rootDir, "signaling-server");
  }

  /**
   * Check if required directories exist
   */
  checkDirectories() {
    console.log("📁 Checking directory structure...");

    const requiredDirs = [
      { path: this.extensionDir, name: "Extension directory" },
      { path: this.signalingDir, name: "Signaling server directory" },
    ];

    for (const dir of requiredDirs) {
      if (fs.existsSync(dir.path)) {
        console.log(`✅ ${dir.name}: ${dir.path}`);
      } else {
        console.error(`❌ ${dir.name} not found: ${dir.path}`);
        return false;
      }
    }

    return true;
  }

  /**
   * Check if required files exist
   */
  checkFiles() {
    console.log("\n📄 Checking required files...");

    const requiredFiles = [
      {
        path: path.join(this.extensionDir, "manifest.json"),
        name: "Extension manifest",
      },
      {
        path: path.join(this.extensionDir, "background.js"),
        name: "Background script",
      },
      { path: path.join(this.extensionDir, "popup.html"), name: "Popup HTML" },
      {
        path: path.join(this.extensionDir, "offscreen.js"),
        name: "Offscreen script",
      },
      {
        path: path.join(this.signalingDir, "server.js"),
        name: "Signaling server",
      },
      {
        path: path.join(this.signalingDir, "package.json"),
        name: "Server package.json",
      },
    ];

    for (const file of requiredFiles) {
      if (fs.existsSync(file.path)) {
        console.log(`✅ ${file.name}: Found`);
      } else {
        console.error(`❌ ${file.name}: Missing`);
        return false;
      }
    }

    return true;
  }

  /**
   * Install automation dependencies
   */
  async installAutomationDeps() {
    console.log("\n📦 Installing automation dependencies...");

    try {
      // Check if package.json exists
      const packagePath = path.join(this.rootDir, "package.json");
      if (!fs.existsSync(packagePath)) {
        console.log("📝 Creating package.json...");
        const packageContent = {
          name: "chrome-extension-automation",
          version: "1.0.0",
          description:
            "Puppeteer automation for Chrome tab screen sharing extension",
          main: "automation-script.js",
          scripts: {
            start: "node automation-cli.js interactive",
            demo: "node demo-automation.js",
          },
          dependencies: {
            puppeteer: "^21.5.0",
          },
        };

        fs.writeFileSync(packagePath, JSON.stringify(packageContent, null, 2));
      }

      // Install dependencies
      console.log("⬇️ Installing Puppeteer...");
      execSync("npm install puppeteer", {
        cwd: this.rootDir,
        stdio: "inherit",
      });

      console.log("✅ Automation dependencies installed");
      return true;
    } catch (error) {
      console.error("❌ Failed to install dependencies:", error.message);
      return false;
    }
  }

  /**
   * Install signaling server dependencies
   */
  async installSignalingDeps() {
    console.log("\n🌐 Installing signaling server dependencies...");

    try {
      execSync("npm install", {
        cwd: this.signalingDir,
        stdio: "inherit",
      });

      console.log("✅ Signaling server dependencies installed");
      return true;
    } catch (error) {
      console.error(
        "❌ Failed to install signaling server dependencies:",
        error.message
      );
      return false;
    }
  }

  /**
   * Test Chrome installation
   */
  async testChrome() {
    console.log("\n🌐 Testing Chrome installation...");

    try {
      const puppeteer = require("puppeteer");
      const browser = await puppeteer.launch({
        headless: true,
        args: ["--no-sandbox", "--disable-setuid-sandbox"],
      });

      const version = await browser.version();
      console.log(`✅ Chrome found: ${version}`);

      await browser.close();
      return true;
    } catch (error) {
      console.error("❌ Chrome test failed:", error.message);
      console.log("💡 Try installing Chrome or Chromium");
      return false;
    }
  }

  /**
   * Create automation scripts if they don't exist
   */
  createScripts() {
    console.log("\n📝 Checking automation scripts...");

    const scripts = [
      "automation-script.js",
      "automation-cli.js",
      "demo-automation.js",
    ];

    for (const script of scripts) {
      const scriptPath = path.join(this.rootDir, script);
      if (fs.existsSync(scriptPath)) {
        console.log(`✅ ${script}: Found`);
      } else {
        console.log(`⚠️ ${script}: Missing (should be created separately)`);
      }
    }
  }

  /**
   * Run setup validation
   */
  async validate() {
    console.log("\n🧪 Running setup validation...");

    try {
      // Test automation script import (skip for now due to ES module complexity)
      const automationPath = path.join(this.rootDir, "automation-script.js");
      if (fs.existsSync(automationPath)) {
        console.log("✅ Automation script: Found");
      } else {
        console.log("⚠️ Automation script: Missing");
      }

      // Test CLI script
      const cliPath = path.join(this.rootDir, "automation-cli.js");
      if (fs.existsSync(cliPath)) {
        console.log("✅ CLI script: Found");
      } else {
        console.log("⚠️ CLI script: Missing");
      }

      // Test demo script
      const demoPath = path.join(this.rootDir, "demo-automation.js");
      if (fs.existsSync(demoPath)) {
        console.log("✅ Demo script: Found");
      } else {
        console.log("⚠️ Demo script: Missing");
      }

      return true;
    } catch (error) {
      console.error("❌ Validation failed:", error.message);
      return false;
    }
  }

  /**
   * Display usage instructions
   */
  showUsage() {
    console.log("\n🎉 Setup Complete!");
    console.log("=".repeat(40));
    console.log("\n📋 Next Steps:");
    console.log("");
    console.log("1. Start the signaling server:");
    console.log("   cd signaling-server && npm start");
    console.log("");
    console.log("2. Run automation demo:");
    console.log("   node demo-automation.js");
    console.log("");
    console.log("3. Use CLI interface:");
    console.log("   node automation-cli.js interactive");
    console.log("");
    console.log("4. Programmatic usage:");
    console.log('   const automation = require("./automation-script");');
    console.log("");
    console.log("📚 See AUTOMATION_GUIDE.md for detailed instructions");
  }

  /**
   * Run complete setup
   */
  async run() {
    console.log("🚀 Chrome Extension Automation Setup");
    console.log("=".repeat(40));

    try {
      // Check environment
      if (!this.checkDirectories()) {
        throw new Error("Required directories missing");
      }

      if (!this.checkFiles()) {
        throw new Error("Required files missing");
      }

      // Install dependencies
      const automationInstalled = await this.installAutomationDeps();
      if (!automationInstalled) {
        throw new Error("Failed to install automation dependencies");
      }

      const signalingInstalled = await this.installSignalingDeps();
      if (!signalingInstalled) {
        throw new Error("Failed to install signaling dependencies");
      }

      // Test Chrome
      const chromeWorking = await this.testChrome();
      if (!chromeWorking) {
        console.log("⚠️ Chrome test failed, but setup will continue");
      }

      // Check scripts
      this.createScripts();

      // Validate
      const validationPassed = await this.validate();
      if (!validationPassed) {
        console.log("⚠️ Validation failed, but basic setup is complete");
      }

      // Show usage
      this.showUsage();

      console.log("\n✅ Setup completed successfully!");
    } catch (error) {
      console.error("\n❌ Setup failed:", error.message);
      console.log("\n💡 Please check the requirements and try again");
      process.exit(1);
    }
  }
}

// Run setup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const setup = new AutomationSetup();
  setup.run();
}

export default AutomationSetup;
