#!/usr/bin/env node

/**
 * Comprehensive Test Suite for Chrome Extension Automation
 * Tests all functionality and provides detailed debugging information
 */

import ExtensionAutomation from './automation-script.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class AutomationTester {
  constructor() {
    this.automation = new ExtensionAutomation();
    this.testResults = [];
    this.currentTest = null;
  }

  /**
   * Log test results
   */
  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      'info': 'ℹ️',
      'success': '✅',
      'error': '❌',
      'warning': '⚠️',
      'debug': '🔍'
    }[type] || 'ℹ️';
    
    const logMessage = `[${timestamp}] ${prefix} ${message}`;
    console.log(logMessage);
    
    if (this.currentTest) {
      this.currentTest.logs.push({ message, type, timestamp });
    }
  }

  /**
   * Start a test
   */
  startTest(name, description) {
    this.currentTest = {
      name,
      description,
      startTime: Date.now(),
      logs: [],
      status: 'running'
    };
    
    this.log(`Starting test: ${name}`, 'info');
    this.log(`Description: ${description}`, 'info');
  }

  /**
   * End a test
   */
  endTest(success, error = null) {
    if (!this.currentTest) return;
    
    this.currentTest.endTime = Date.now();
    this.currentTest.duration = this.currentTest.endTime - this.currentTest.startTime;
    this.currentTest.status = success ? 'passed' : 'failed';
    this.currentTest.error = error;
    
    this.testResults.push(this.currentTest);
    
    if (success) {
      this.log(`Test passed: ${this.currentTest.name} (${this.currentTest.duration}ms)`, 'success');
    } else {
      this.log(`Test failed: ${this.currentTest.name} - ${error}`, 'error');
    }
    
    this.currentTest = null;
  }

  /**
   * Test 1: Environment Setup
   */
  async testEnvironmentSetup() {
    this.startTest('Environment Setup', 'Check if all required files and directories exist');
    
    try {
      // Check extension directory
      const extensionPath = path.join(__dirname, 'tab-screen-share-extension');
      if (!fs.existsSync(extensionPath)) {
        throw new Error(`Extension directory not found: ${extensionPath}`);
      }
      this.log(`Extension directory found: ${extensionPath}`, 'success');

      // Check required extension files
      const requiredFiles = [
        'manifest.json',
        'background.js',
        'popup.html',
        'popup.js',
        'offscreen.html',
        'offscreen.js'
      ];

      for (const file of requiredFiles) {
        const filePath = path.join(extensionPath, file);
        if (!fs.existsSync(filePath)) {
          throw new Error(`Required extension file missing: ${file}`);
        }
        this.log(`Extension file found: ${file}`, 'success');
      }

      // Check signaling server
      const signalingPath = path.join(__dirname, 'signaling-server');
      if (!fs.existsSync(signalingPath)) {
        throw new Error(`Signaling server directory not found: ${signalingPath}`);
      }
      this.log(`Signaling server directory found: ${signalingPath}`, 'success');

      this.endTest(true);
      return true;
      
    } catch (error) {
      this.endTest(false, error.message);
      return false;
    }
  }

  /**
   * Test 2: Chrome Launch
   */
  async testChromeLaunch() {
    this.startTest('Chrome Launch', 'Launch Chrome with extension loaded');
    
    try {
      this.log('Launching Chrome with extension...', 'info');
      await this.automation.launchChrome();
      
      if (!this.automation.browser) {
        throw new Error('Browser instance not created');
      }
      
      this.log('Chrome launched successfully', 'success');
      
      // Check if browser is connected
      const isConnected = this.automation.browser.isConnected();
      if (!isConnected) {
        throw new Error('Browser is not connected');
      }
      
      this.log('Browser connection verified', 'success');
      
      this.endTest(true);
      return true;
      
    } catch (error) {
      this.endTest(false, error.message);
      return false;
    }
  }

  /**
   * Test 3: Extension ID Detection
   */
  async testExtensionIdDetection() {
    this.startTest('Extension ID Detection', 'Find and verify extension ID');
    
    try {
      this.log('Detecting extension ID...', 'info');
      await this.automation.getExtensionId();
      
      if (!this.automation.extensionId) {
        throw new Error('Extension ID not detected');
      }
      
      this.log(`Extension ID found: ${this.automation.extensionId}`, 'success');
      
      // Verify extension ID format
      const extensionIdRegex = /^[a-p]{32}$/;
      if (!extensionIdRegex.test(this.automation.extensionId)) {
        this.log(`Warning: Extension ID format unusual: ${this.automation.extensionId}`, 'warning');
      }
      
      this.endTest(true);
      return true;
      
    } catch (error) {
      this.endTest(false, error.message);
      return false;
    }
  }

  /**
   * Test 4: Test Page Setup
   */
  async testPageSetup() {
    this.startTest('Test Page Setup', 'Navigate to test page and setup CDP');
    
    try {
      this.log('Setting up test page...', 'info');
      await this.automation.setupTestPage();
      
      if (!this.automation.page) {
        throw new Error('Page instance not created');
      }
      
      const url = this.automation.page.url();
      this.log(`Test page loaded: ${url}`, 'success');
      
      // Verify page is accessible
      const title = await this.automation.page.title();
      this.log(`Page title: ${title}`, 'info');
      
      this.endTest(true);
      return true;
      
    } catch (error) {
      this.endTest(false, error.message);
      return false;
    }
  }

  /**
   * Test 5: Extension Popup Access
   */
  async testExtensionPopupAccess() {
    this.startTest('Extension Popup Access', 'Open and interact with extension popup');
    
    try {
      this.log('Opening extension popup...', 'info');
      const popupPage = await this.automation.openExtensionPopup();
      
      if (!popupPage) {
        throw new Error('Failed to open popup page');
      }
      
      const popupUrl = popupPage.url();
      this.log(`Popup opened: ${popupUrl}`, 'success');
      
      // Check if popup contains expected elements
      this.log('Checking popup elements...', 'info');
      
      const elements = [
        '#startCaptureBtn',
        '#stopCaptureBtn',
        '#startWebRTCBtn',
        '#stopWebRTCBtn',
        '#captureStatus'
      ];
      
      for (const selector of elements) {
        const element = await popupPage.$(selector);
        if (!element) {
          throw new Error(`Required popup element not found: ${selector}`);
        }
        this.log(`Element found: ${selector}`, 'success');
      }
      
      await popupPage.close();
      this.endTest(true);
      return true;
      
    } catch (error) {
      this.endTest(false, error.message);
      return false;
    }
  }

  /**
   * Test 6: Tab Capture Functionality
   */
  async testTabCapture() {
    this.startTest('Tab Capture', 'Test tab capture start/stop functionality');
    
    try {
      this.log('Testing tab capture...', 'info');
      
      // Test start capture
      this.log('Starting tab capture...', 'info');
      const captureResult = await this.automation.startCapture();
      
      if (!captureResult) {
        throw new Error('Failed to start tab capture');
      }
      
      this.log('Tab capture started successfully', 'success');
      
      // Verify capture status
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for capture to initialize
      
      const status = await this.automation.getStatus();
      if (!status.capturing) {
        throw new Error('Capture status not updated correctly');
      }
      
      this.log('Capture status verified', 'success');
      
      this.endTest(true);
      return true;
      
    } catch (error) {
      this.endTest(false, error.message);
      return false;
    }
  }

  /**
   * Test 7: WebRTC Streaming
   */
  async testWebRTCStreaming() {
    this.startTest('WebRTC Streaming', 'Test WebRTC streaming functionality');
    
    try {
      this.log('Testing WebRTC streaming...', 'info');
      
      // Ensure capture is active first
      if (!this.automation.isCapturing) {
        this.log('Starting capture first...', 'info');
        await this.automation.startCapture();
      }
      
      // Test WebRTC streaming
      this.log('Starting WebRTC stream...', 'info');
      const streamResult = await this.automation.startWebRTCStream();
      
      if (!streamResult) {
        throw new Error('Failed to start WebRTC stream');
      }
      
      this.log('WebRTC stream started successfully', 'success');
      
      // Verify streaming status
      await new Promise(resolve => setTimeout(resolve, 3000)); // Wait for stream to establish
      
      const status = await this.automation.getStatus();
      if (!status.streaming) {
        this.log('Warning: Streaming status not updated (may be normal)', 'warning');
      } else {
        this.log('Streaming status verified', 'success');
      }
      
      this.endTest(true);
      return true;
      
    } catch (error) {
      this.endTest(false, error.message);
      return false;
    }
  }

  /**
   * Test 8: Status Monitoring
   */
  async testStatusMonitoring() {
    this.startTest('Status Monitoring', 'Test status reporting accuracy');
    
    try {
      this.log('Testing status monitoring...', 'info');
      
      const status = await this.automation.getStatus();
      this.log(`Current status: ${JSON.stringify(status)}`, 'info');
      
      // Verify status object structure
      const requiredFields = ['capturing', 'streaming', 'ready'];
      for (const field of requiredFields) {
        if (!(field in status)) {
          throw new Error(`Status missing required field: ${field}`);
        }
      }
      
      this.log('Status structure verified', 'success');
      
      this.endTest(true);
      return true;
      
    } catch (error) {
      this.endTest(false, error.message);
      return false;
    }
  }

  /**
   * Test 9: Cleanup
   */
  async testCleanup() {
    this.startTest('Cleanup', 'Test proper resource cleanup');
    
    try {
      this.log('Testing cleanup...', 'info');
      
      // Stop streaming if active
      if (this.automation.isStreaming || this.automation.isCapturing) {
        this.log('Stopping active streaming/capture...', 'info');
        await this.automation.stopStreaming();
      }
      
      // Cleanup resources
      await this.automation.cleanup();
      
      this.log('Cleanup completed successfully', 'success');
      
      this.endTest(true);
      return true;
      
    } catch (error) {
      this.endTest(false, error.message);
      return false;
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 Starting Comprehensive Automation Tests');
    console.log('=' .repeat(50));
    
    const tests = [
      () => this.testEnvironmentSetup(),
      () => this.testChromeLaunch(),
      () => this.testExtensionIdDetection(),
      () => this.testPageSetup(),
      () => this.testExtensionPopupAccess(),
      () => this.testTabCapture(),
      () => this.testWebRTCStreaming(),
      () => this.testStatusMonitoring(),
      () => this.testCleanup()
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
      try {
        const result = await test();
        if (result) {
          passed++;
        } else {
          failed++;
        }
      } catch (error) {
        this.log(`Unexpected test error: ${error.message}`, 'error');
        failed++;
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    this.printTestSummary(passed, failed);
    return { passed, failed, results: this.testResults };
  }

  /**
   * Print test summary
   */
  printTestSummary(passed, failed) {
    console.log('\n📊 Test Summary');
    console.log('=' .repeat(30));
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
    
    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(test => test.status === 'failed')
        .forEach(test => {
          console.log(`  - ${test.name}: ${test.error}`);
        });
    }
    
    console.log('\n📝 Detailed Results:');
    this.testResults.forEach(test => {
      const status = test.status === 'passed' ? '✅' : '❌';
      console.log(`  ${status} ${test.name} (${test.duration}ms)`);
    });
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new AutomationTester();
  
  process.on('SIGINT', async () => {
    console.log('\n🛑 Test interrupted');
    await tester.automation.cleanup();
    process.exit(0);
  });
  
  tester.runAllTests()
    .then(results => {
      if (results.failed > 0) {
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    });
}

export default AutomationTester;
